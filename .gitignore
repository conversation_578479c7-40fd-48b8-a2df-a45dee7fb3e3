# D&L Garden Center - Git Ignore File

# Sensitive Configuration Files
includes/config.php.local
includes/config-local.php
.env
.env.local

# Database Files
*.sql.backup
*.db
*.sqlite

# Log Files
*.log
logs/
error_log

# Cache and Temporary Files
cache/
tmp/
temp/
*.tmp
*.cache

# Upload Directories (keep structure but ignore uploads)
uploads/*
!uploads/.gitkeep
assets/images/uploads/*
!assets/images/uploads/.gitkeep

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Node.js (if using build tools)
node_modules/
npm-debug.log
yarn-error.log
package-lock.json
yarn.lock

# Composer (if using PHP dependencies)
vendor/
composer.lock

# System Files
.htaccess.local
robots.txt.local

# Backup Files
*.backup
*.bak
*.old

# Development Tools
phpunit.xml
.phpunit.result.cache

# Local Development
test-db-connection.php.local
setup-database.sh.local

# OS Generated Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Security
*.pem
*.key
*.crt
ssl/
certificates/
