# MariaDB Setup for D&L Garden Center Website

## Quick Setup Guide

### Step 1: Check Current Status

MariaDB is already installed and running on your system. You can verify this with:

```bash
systemctl status mariadb
```

### Step 2: Test Database Connection

Visit the database connection test page:
```
http://localhost:8000/test-db-connection.php
```

This will show you the current database status and what needs to be configured.

### Step 3: Run Database Setup

**Option A: Automated Setup (Recommended)**

Run the provided setup script:
```bash
./setup-database.sh
```

**Option B: Manual Setup**

1. Connect to MariaDB as root:
```bash
sudo mysql
```

2. Create the database and user:
```sql
CREATE DATABASE dlgarden_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'dlgarden_user'@'localhost' IDENTIFIED BY 'dlgarden_pass_2025!';
GRANT ALL PRIVILEGES ON dlgarden_db.* TO 'dlgarden_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### Step 4: Install Database Schema

Once the database and user are created, install the website schema:

Visit: `http://localhost:8000/sql/install.php?install=confirm`

This will:
- Create all necessary tables
- Insert sample data
- Create a default admin user
- Test the connection

### Step 5: Verify Installation

After running the installer, you should see:
- ✅ Database connection successful
- ✅ Tables created (categories, products, gallery_images, etc.)
- ✅ Sample data inserted
- ✅ Default admin user created

## Database Configuration

The website is configured to use:
- **Host:** localhost
- **Database:** dlgarden_db
- **Username:** dlgarden_user
- **Password:** dlgarden_pass_2025!

You can modify these settings in `includes/config.php` if needed.

## Troubleshooting

### Common Issues

**1. "Access denied for user 'root'"**
- MariaDB on Kali Linux uses socket authentication for root
- Use `sudo mysql` instead of `mysql -u root -p`

**2. "PDO MySQL extension not loaded"**
- Install PHP MySQL extension: `sudo apt install php-mysql`
- Restart the web server

**3. "Connection refused"**
- Start MariaDB: `sudo systemctl start mariadb`
- Enable auto-start: `sudo systemctl enable mariadb`

**4. "Database does not exist"**
- Run the setup script or create manually as shown above

### Checking MariaDB Status

```bash
# Check if MariaDB is running
systemctl status mariadb

# Start MariaDB if not running
sudo systemctl start mariadb

# Enable MariaDB to start on boot
sudo systemctl enable mariadb

# Connect to MariaDB
sudo mysql

# Check databases
SHOW DATABASES;

# Check users
SELECT User, Host FROM mysql.user;
```

### Reset Database (if needed)

If you need to start over:

```sql
DROP DATABASE IF EXISTS dlgarden_db;
DROP USER IF EXISTS 'dlgarden_user'@'localhost';
```

Then run the setup again.

## Security Notes

### Production Recommendations

When deploying to production:

1. **Change the database password:**
```sql
ALTER USER 'dlgarden_user'@'localhost' IDENTIFIED BY 'your_secure_password_here';
```

2. **Update the configuration:**
Edit `includes/config.php` and change:
```php
define('DB_PASS', 'your_secure_password_here');
define('DEVELOPMENT_MODE', false);
```

3. **Secure MariaDB installation:**
```bash
sudo mysql_secure_installation
```

4. **Configure firewall:**
```bash
sudo ufw allow 3306  # Only if remote access needed
```

### File Permissions

Ensure proper file permissions:
```bash
chmod 600 includes/config.php  # Protect config file
chmod 755 sql/install.php      # Allow installer access
```

## Database Schema Overview

The database includes these main tables:

- **categories** - Product categories
- **products** - Product information
- **product_pricing** - Pickup and delivery pricing
- **product_images** - Product photos
- **gallery_categories** - Photo gallery categories
- **gallery_images** - Gallery photos
- **coupons** - Promotional offers
- **content_blocks** - Dynamic page content
- **contact_submissions** - Contact form submissions
- **admin_users** - Admin panel users
- **settings** - Site configuration

## Next Steps

After database setup:

1. **Test the website:** http://localhost:8000
2. **Check admin panel:** http://localhost:8000/admin/ (admin/admin123)
3. **Upload product photos** to the gallery
4. **Customize content** through the admin panel
5. **Update business information** in the configuration

## Support

If you encounter issues:

1. Check the database connection test page
2. Review MariaDB logs: `sudo journalctl -u mariadb`
3. Verify PHP extensions: `php -m | grep pdo`
4. Test manual connection: `sudo mysql -u dlgarden_user -p dlgarden_db`
