# 🌱 D&L Garden Center Website

[![PHP](https://img.shields.io/badge/PHP-8.2+-777BB4?style=flat&logo=php&logoColor=white)](https://php.net)
[![Bootstrap](https://img.shields.io/badge/Bootstrap-5.3-7952B3?style=flat&logo=bootstrap&logoColor=white)](https://getbootstrap.com)
[![MariaDB](https://img.shields.io/badge/MariaDB-10.6+-003545?style=flat&logo=mariadb&logoColor=white)](https://mariadb.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

Modern, responsive website for **D&L Garden Center** - a family-owned garden center serving the Detroit area since 1978. Built with PHP, Bootstrap 5, and MariaDB.

![D&L Garden Center Homepage](assets/images/head_image.jpg)

## 🚀 Live Demo

Visit the live website: [D&L Garden Center](http://localhost:8000) *(Update with actual URL)*

## Features

- **Modern Responsive Design** - Bootstrap 5 with custom styling
- **SEO-Optimized URLs** - Clean `/page-name/` structure for better SEO
- **PHP Backend** - Object-oriented PHP with PDO database integration
- **MariaDB Database** - Comprehensive schema for products, pricing, gallery, content
- **Fancybox Gallery** - Photo galleries without top center tools (as requested)
- **Mobile-First Design** - Optimized for all devices
- **Security Features** - CSRF protection, input sanitization, secure headers

## Technology Stack

- **PHP 8.2+** - Server-side logic
- **Bootstrap 5** - Responsive CSS framework
- **JavaScript** - Enhanced user experience
- **Fancybox 5** - Image galleries
- **MariaDB/MySQL** - Database
- **Apache/Nginx** - Web server (with .htaccess for Apache)

## Quick Start

### 1. Development Server (PHP Built-in)

```bash
# Navigate to project directory
cd /path/to/DL-Garden-Center-2025

# Start PHP development server
php -S localhost:8000

# Open in browser
http://localhost:8000
```

### 2. Database Setup (Optional)

The website works without a database, but for full functionality:

```bash
# Install MariaDB/MySQL
sudo apt install mariadb-server  # On Kali Linux

# Create database and user
sudo mysql
CREATE DATABASE dlgarden_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'dlgarden_user'@'localhost' IDENTIFIED BY 'dlgarden_pass_2025!';
GRANT ALL PRIVILEGES ON dlgarden_db.* TO 'dlgarden_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# Run database installation
http://localhost:8000/sql/install.php?install=confirm
```

## Project Structure

```
/
├── index.php                 # Homepage
├── contact/index.php         # Contact page
├── products/index.php        # Products overview
├── pricing/
│   ├── pickup/index.php      # Pickup pricing
│   └── delivery/index.php    # Delivery pricing
├── gallery/index.php         # Photo gallery
├── coupons/index.php         # Coupons page
├── includes/
│   ├── config.php           # Configuration
│   ├── database.php         # Database class
│   ├── functions.php        # Utility functions
│   ├── Product.php          # Product management
│   ├── Gallery.php          # Gallery management
│   ├── Content.php          # Content management
│   ├── header.php           # Site header
│   └── footer.php           # Site footer
├── assets/
│   ├── css/style.css        # Custom styles
│   ├── js/main.js           # Custom JavaScript
│   └── images/              # Site images
├── sql/
│   ├── schema.sql           # Database schema
│   └── install.php          # Database installer
└── .htaccess                # Apache configuration
```

## Pages Available

### Main Pages
- **Homepage** - `/` - Welcome, features, contact info, dealer information
- **Products Overview** - `/products/` - Product categories and services overview
- **Pricing Overview** - `/pricing/` - Interactive price calculator and options
- **Services** - `/services/` - Complete service offerings and process
- **Gallery** - `/gallery/` - Photo gallery with filtering and Fancybox
- **Coupons** - `/coupons/` - Seasonal offers and printable coupons
- **Contact** - `/contact/` - Enhanced contact form, info, and map

### Product Pages
- **Bulk Materials** - `/products/bulk-materials/` - Detailed bulk material catalog
- **Bagged Supplies** - `/products/bagged-supplies/` - Pre-packaged materials and supplies

### Pricing Pages
- **Pickup Pricing** - `/pricing/pickup/` - Current pickup prices with coverage guide
- **Delivery Pricing** - `/pricing/delivery/` - Delivery prices, terms, and service area

### Additional Pages
- **404 Error Page** - Custom error handling with helpful navigation

## Configuration

Edit `includes/config.php` to customize:

- Site name and contact information
- Database connection settings
- File upload settings
- Development/production mode

## Key Features Implemented

### SEO Optimization
- Clean URL structure (`/page-name/`) for all pages
- Comprehensive meta tags and structured data
- Semantic HTML markup throughout
- Image alt tags and descriptions
- Breadcrumb navigation
- XML sitemap ready structure

### Modern User Experience
- **Interactive Price Calculator** - Real-time pricing estimates
- **Advanced Gallery** - Filterable photo gallery with Fancybox (no top center tools)
- **Responsive Contact Form** - With validation and database storage
- **Mobile-First Design** - Optimized for all devices
- **Progressive Enhancement** - Works without JavaScript

### Security & Performance
- CSRF token protection on all forms
- Input sanitization and validation
- SQL injection prevention (PDO prepared statements)
- File upload validation and security
- Security headers via .htaccess
- Browser caching and compression
- Efficient database queries with error handling

### Content Management
- **Dynamic Content System** - Database-driven content blocks
- **Product Management** - Comprehensive product catalog system
- **Gallery Management** - Photo categorization and management
- **Settings System** - Configurable site settings
- **Contact Management** - Form submission tracking

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers

## Development Notes

- Built for Kali Linux environment
- Uses MariaDB (MySQL compatible)
- Follows modern PHP practices
- Bootstrap 5 for responsive design
- Fancybox configured without top center tools
- SEO-friendly URL structure as requested

## Completed Features

### ✅ **Fully Functional Website**
- **13 Complete Pages** - All major pages implemented and tested
- **Database Integration** - MariaDB with comprehensive schema
- **Modern Design** - Bootstrap 5 responsive framework
- **SEO Optimized** - Clean URLs and proper meta tags
- **Security Hardened** - CSRF protection and input validation
- **Performance Optimized** - Caching, compression, and efficient queries

### ✅ **Business Features**
- **Interactive Price Calculator** - Real-time estimates for materials
- **Comprehensive Product Catalog** - Bulk materials and bagged supplies
- **Professional Gallery** - Filterable photo gallery with Fancybox
- **Contact Management** - Form submissions saved to database
- **Seasonal Promotions** - Coupons and special offers system
- **Service Information** - Complete service offerings and process

### ✅ **Technical Excellence**
- **Error Handling** - Graceful degradation when database unavailable
- **Mobile Responsive** - Perfect on all devices
- **Accessibility** - Semantic HTML and proper ARIA labels
- **Browser Compatibility** - Works on all modern browsers
- **Development Ready** - Easy to extend and customize

## Next Steps (Optional Enhancements)

1. **Admin Panel** - Create admin interface for content management
2. **Product Images** - Upload actual product photos to gallery
3. **Email Integration** - Set up contact form email notifications
4. **Analytics** - Add Google Analytics tracking
5. **Production Deployment** - Configure for live server

## Support

For questions or issues, refer to the documentation in each PHP file or contact the development team.
