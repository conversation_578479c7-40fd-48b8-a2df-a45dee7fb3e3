# D&L Garden Center Website

Modern, responsive website for D&L Garden Center built with PHP, Bootstrap 5, and MariaDB.

## Features

- **Modern Responsive Design** - Bootstrap 5 with custom styling
- **SEO-Optimized URLs** - Clean `/page-name/` structure for better SEO
- **PHP Backend** - Object-oriented PHP with PDO database integration
- **MariaDB Database** - Comprehensive schema for products, pricing, gallery, content
- **Fancybox Gallery** - Photo galleries without top center tools (as requested)
- **Mobile-First Design** - Optimized for all devices
- **Security Features** - CSRF protection, input sanitization, secure headers

## Technology Stack

- **PHP 8.2+** - Server-side logic
- **Bootstrap 5** - Responsive CSS framework
- **JavaScript** - Enhanced user experience
- **Fancybox 5** - Image galleries
- **MariaDB/MySQL** - Database
- **Apache/Nginx** - Web server (with .htaccess for Apache)

## Quick Start

### 1. Development Server (PHP Built-in)

```bash
# Navigate to project directory
cd /path/to/DL-Garden-Center-2025

# Start PHP development server
php -S localhost:8000

# Open in browser
http://localhost:8000
```

### 2. Database Setup (Optional)

The website works without a database, but for full functionality:

```bash
# Install MariaDB/MySQL
sudo apt install mariadb-server  # On Kali Linux

# Create database and user
sudo mysql
CREATE DATABASE dlgarden_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'dlgarden_user'@'localhost' IDENTIFIED BY 'dlgarden_pass_2025!';
GRANT ALL PRIVILEGES ON dlgarden_db.* TO 'dlgarden_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# Run database installation
http://localhost:8000/sql/install.php?install=confirm
```

## Project Structure

```
/
├── index.php                 # Homepage
├── contact/index.php         # Contact page
├── products/index.php        # Products overview
├── pricing/
│   ├── pickup/index.php      # Pickup pricing
│   └── delivery/index.php    # Delivery pricing
├── gallery/index.php         # Photo gallery
├── coupons/index.php         # Coupons page
├── includes/
│   ├── config.php           # Configuration
│   ├── database.php         # Database class
│   ├── functions.php        # Utility functions
│   ├── Product.php          # Product management
│   ├── Gallery.php          # Gallery management
│   ├── Content.php          # Content management
│   ├── header.php           # Site header
│   └── footer.php           # Site footer
├── assets/
│   ├── css/style.css        # Custom styles
│   ├── js/main.js           # Custom JavaScript
│   └── images/              # Site images
├── sql/
│   ├── schema.sql           # Database schema
│   └── install.php          # Database installer
└── .htaccess                # Apache configuration
```

## Pages Available

- **Homepage** - `/` - Welcome, features, contact info
- **Products** - `/products/` - Product categories and overview
- **Pickup Pricing** - `/pricing/pickup/` - Current pickup prices
- **Delivery Pricing** - `/pricing/delivery/` - Delivery prices and terms
- **Gallery** - `/gallery/` - Photo gallery with Fancybox
- **Coupons** - `/coupons/` - Current offers and printable coupons
- **Contact** - `/contact/` - Contact form, info, and map

## Configuration

Edit `includes/config.php` to customize:

- Site name and contact information
- Database connection settings
- File upload settings
- Development/production mode

## Key Features Implemented

### SEO Optimization
- Clean URL structure (`/page-name/`)
- Meta tags and structured data
- Semantic HTML markup
- Image alt tags and descriptions

### Security
- CSRF token protection
- Input sanitization
- SQL injection prevention (PDO prepared statements)
- File upload validation
- Security headers via .htaccess

### Responsive Design
- Mobile-first approach
- Bootstrap 5 grid system
- Touch-friendly navigation
- Optimized images

### Performance
- Minified CSS/JS from CDN
- Image optimization
- Browser caching headers
- Efficient database queries

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers

## Development Notes

- Built for Kali Linux environment
- Uses MariaDB (MySQL compatible)
- Follows modern PHP practices
- Bootstrap 5 for responsive design
- Fancybox configured without top center tools
- SEO-friendly URL structure as requested

## Next Steps

1. **Database Setup** - Run the database installer for full functionality
2. **Content Management** - Add admin panel for easy content updates
3. **Image Gallery** - Upload product and facility photos
4. **Testing** - Test all forms and functionality
5. **Production** - Configure for live server deployment

## Support

For questions or issues, refer to the documentation in each PHP file or contact the development team.
