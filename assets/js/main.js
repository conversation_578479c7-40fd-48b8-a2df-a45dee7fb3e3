/**
 * D&L Garden Center - Main JavaScript
 * Modern JavaScript for enhanced user experience
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize all components
    initBackToTop();
    initSmoothScrolling();
    initFancybox();
    initFormValidation();
    initAnimations();
    initMobileMenu();
    initHeroSlideshow();

    console.log('D&L Garden Center website initialized');
});

/**
 * Back to Top Button
 */
function initBackToTop() {
    const backToTopButton = document.getElementById('btn-back-to-top');
    
    if (backToTopButton) {
        // Show/hide button based on scroll position
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopButton.style.display = 'block';
                backToTopButton.style.opacity = '1';
            } else {
                backToTopButton.style.opacity = '0';
                setTimeout(() => {
                    if (window.pageYOffset <= 300) {
                        backToTopButton.style.display = 'none';
                    }
                }, 300);
            }
        });
        
        // Smooth scroll to top
        backToTopButton.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

/**
 * Smooth Scrolling for Anchor Links
 */
function initSmoothScrolling() {
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                e.preventDefault();
                
                const headerHeight = document.querySelector('.site-header').offsetHeight;
                const targetPosition = targetElement.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

/**
 * Initialize Fancybox for Image Galleries
 */
function initFancybox() {
    // Configure Fancybox without top center tools as requested
    if (typeof Fancybox !== 'undefined') {
        Fancybox.bind('[data-fancybox]', {
            // Disable top center toolbar
            Toolbar: {
                display: {
                    left: ['infobar'],
                    middle: [],
                    right: ['slideshow', 'thumbs', 'close']
                }
            },
            // Customize appearance
            Thumbs: {
                autoStart: false,
            },
            // Animation settings
            showClass: 'fancybox-fadeIn',
            hideClass: 'fancybox-fadeOut',
            // Image settings
            Images: {
                zoom: true,
                Panzoom: {
                    maxScale: 3
                }
            }
        });
    }
}

/**
 * Form Validation and Enhancement
 */
function initFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        });
    });
    
    // Phone number formatting
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    phoneInputs.forEach(input => {
        input.addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.length >= 6) {
                value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
            } else if (value.length >= 3) {
                value = value.replace(/(\d{3})(\d{0,3})/, '($1) $2');
            }
            this.value = value;
        });
    });
}

/**
 * Scroll-triggered Animations
 */
function initAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.card, .pricing-table, .gallery-item');
    animatedElements.forEach(el => {
        observer.observe(el);
    });
}

/**
 * Mobile Menu Enhancements
 */
function initMobileMenu() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (navbarToggler && navbarCollapse) {
        // Close mobile menu when clicking on a link
        const navLinks = navbarCollapse.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (window.innerWidth < 992) {
                    const bsCollapse = new bootstrap.Collapse(navbarCollapse, {
                        toggle: false
                    });
                    bsCollapse.hide();
                }
            });
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const isClickInsideNav = navbarCollapse.contains(event.target) ||
                                   navbarToggler.contains(event.target);

            if (!isClickInsideNav && navbarCollapse.classList.contains('show')) {
                const bsCollapse = new bootstrap.Collapse(navbarCollapse, {
                    toggle: false
                });
                bsCollapse.hide();
            }
        });
    }
}

/**
 * Hero Slideshow Enhancements
 */
function initHeroSlideshow() {
    const heroCarousel = document.getElementById('heroCarousel');
    const heroLoading = document.querySelector('.hero-loading');

    if (heroCarousel) {
        // Hide loading indicator once carousel is ready
        setTimeout(() => {
            if (heroLoading) {
                heroLoading.style.opacity = '0';
                setTimeout(() => {
                    heroLoading.style.display = 'none';
                }, 500);
            }
        }, 1000);
        // Pause slideshow on hover
        heroCarousel.addEventListener('mouseenter', function() {
            const carousel = bootstrap.Carousel.getInstance(heroCarousel);
            if (carousel) {
                carousel.pause();
            }
        });

        // Resume slideshow when mouse leaves
        heroCarousel.addEventListener('mouseleave', function() {
            const carousel = bootstrap.Carousel.getInstance(heroCarousel);
            if (carousel) {
                carousel.cycle();
            }
        });

        // Add keyboard navigation
        document.addEventListener('keydown', function(event) {
            const carousel = bootstrap.Carousel.getInstance(heroCarousel);
            if (carousel) {
                if (event.key === 'ArrowLeft') {
                    event.preventDefault();
                    carousel.prev();
                } else if (event.key === 'ArrowRight') {
                    event.preventDefault();
                    carousel.next();
                }
            }
        });

        // Preload next slide images for better performance
        const slides = heroCarousel.querySelectorAll('.carousel-item');
        slides.forEach((slide, index) => {
            const bgImage = slide.querySelector('.hero-slide');
            if (bgImage) {
                const imageUrl = bgImage.style.backgroundImage.match(/url\(['"]?([^'")]+)['"]?\)/);
                if (imageUrl && imageUrl[1]) {
                    const img = new Image();
                    img.src = imageUrl[1];
                }
            }
        });

        // Add touch/swipe support for mobile
        let startX = 0;
        let endX = 0;

        heroCarousel.addEventListener('touchstart', function(event) {
            startX = event.touches[0].clientX;
        });

        heroCarousel.addEventListener('touchend', function(event) {
            endX = event.changedTouches[0].clientX;
            handleSwipe();
        });

        function handleSwipe() {
            const carousel = bootstrap.Carousel.getInstance(heroCarousel);
            if (carousel) {
                const swipeThreshold = 50;
                const diff = startX - endX;

                if (Math.abs(diff) > swipeThreshold) {
                    if (diff > 0) {
                        // Swipe left - next slide
                        carousel.next();
                    } else {
                        // Swipe right - previous slide
                        carousel.prev();
                    }
                }
            }
        }

        // Add slide change event listener for analytics or other tracking
        heroCarousel.addEventListener('slide.bs.carousel', function(event) {
            console.log('Slide changed to:', event.to);

            // Reset animations for the new slide
            const newSlide = event.relatedTarget;
            const animatedElements = newSlide.querySelectorAll('[class*="slide-in-"]');
            animatedElements.forEach(element => {
                element.style.animation = 'none';
                element.offsetHeight; // Trigger reflow
                element.style.animation = null;
            });
        });
    }
}

/**
 * Utility Functions
 */

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Debounce function for performance
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Show loading spinner
function showLoading(element) {
    if (element) {
        element.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
    }
}

// Hide loading spinner
function hideLoading(element, originalContent) {
    if (element) {
        element.innerHTML = originalContent;
    }
}

// Show toast notification
function showToast(message, type = 'success') {
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    
    const toastElement = document.createElement('div');
    toastElement.className = `toast align-items-center text-white bg-${type} border-0`;
    toastElement.setAttribute('role', 'alert');
    toastElement.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toastElement);
    
    const toast = new bootstrap.Toast(toastElement);
    toast.show();
    
    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

// Create toast container if it doesn't exist
function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    container.style.zIndex = '1055';
    document.body.appendChild(container);
    return container;
}

// AJAX helper function
function makeRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    };
    
    const config = { ...defaultOptions, ...options };
    
    return fetch(url, config)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error('Request failed:', error);
            showToast('An error occurred. Please try again.', 'danger');
            throw error;
        });
}

// Export functions for use in other scripts
window.DLGarden = {
    formatCurrency,
    debounce,
    showLoading,
    hideLoading,
    showToast,
    makeRequest
};
