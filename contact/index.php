<?php
/**
 * D&L Garden Center - Contact Page
 * Contact information, form, and map
 */

define('DL_GARDEN_CENTER', true);
require_once '../includes/config.php';

// Page meta data
$pageTitle = 'Contact Us - ' . SITE_NAME;
$pageDescription = 'Contact D&L Garden Center in Taylor, MI. Get directions, hours, phone number, and send us a message about your landscaping needs.';
$pageKeywords = 'contact, directions, hours, phone, Taylor Michigan, garden center, landscaping supplies';

// Handle form submission
$formMessage = '';
$formType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Verify CSRF token
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid form submission');
        }
        
        // Save contact submission
        $submissionId = $contentManager->saveContactSubmission($_POST);
        
        $formMessage = 'Thank you for your message! We will get back to you soon.';
        $formType = 'success';
        
        // Clear form data
        $_POST = [];
        
    } catch (Exception $e) {
        $formMessage = 'Error: ' . $e->getMessage();
        $formType = 'danger';
    }
}

// Get contact information
$contactInfo = $contentManager->getContactInfo();

include '../includes/header.php';
?>

<!-- Page Header -->
<section class="page-header bg-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1>Contact Us</h1>
                <p class="lead">Get in touch with D&L Garden Center</p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Information -->
<section class="contact-info py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mb-5">
                <h2>About D&L Garden Center</h2>
                <p class="lead">D&L Garden Center is a locally owned, family-run business. It has been at its current location on Ecorse Rd. in Taylor since 1978.</p>
                <p>D&L Garden Center is a complete garden center carrying a full line of premium lawn and garden products including shrubs, trees, fertilizers, bulk landscape materials and power equipment for both the homeowner and landscaper.</p>
                
                <!-- Contact Form -->
                <div class="mt-5">
                    <h3>Send Us a Message</h3>
                    
                    <?php if ($formMessage): ?>
                        <div class="alert alert-<?php echo $formType; ?> alert-dismissible fade show" role="alert">
                            <?php echo htmlspecialchars($formMessage); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Name *</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="name" 
                                       name="name" 
                                       value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>"
                                       required>
                                <div class="invalid-feedback">Please provide your name.</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" 
                                       class="form-control" 
                                       id="email" 
                                       name="email" 
                                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                       required>
                                <div class="invalid-feedback">Please provide a valid email address.</div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone</label>
                                <input type="tel" 
                                       class="form-control" 
                                       id="phone" 
                                       name="phone" 
                                       value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="subject" class="form-label">Subject</label>
                                <select class="form-select" id="subject" name="subject">
                                    <option value="">Select a subject</option>
                                    <option value="General Inquiry" <?php echo ($_POST['subject'] ?? '') === 'General Inquiry' ? 'selected' : ''; ?>>General Inquiry</option>
                                    <option value="Product Question" <?php echo ($_POST['subject'] ?? '') === 'Product Question' ? 'selected' : ''; ?>>Product Question</option>
                                    <option value="Pricing" <?php echo ($_POST['subject'] ?? '') === 'Pricing' ? 'selected' : ''; ?>>Pricing</option>
                                    <option value="Delivery" <?php echo ($_POST['subject'] ?? '') === 'Delivery' ? 'selected' : ''; ?>>Delivery</option>
                                    <option value="Equipment Service" <?php echo ($_POST['subject'] ?? '') === 'Equipment Service' ? 'selected' : ''; ?>>Equipment Service</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Message *</label>
                            <textarea class="form-control" 
                                      id="message" 
                                      name="message" 
                                      rows="5" 
                                      required><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                            <div class="invalid-feedback">Please provide your message.</div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg">Send Message</button>
                    </form>
                </div>
            </div>
            
            <div class="col-lg-4">
                <!-- Contact Details -->
                <div class="contact-details bg-light p-4 rounded">
                    <h3>Contact Information</h3>
                    
                    <div class="contact-item mb-3">
                        <h5><i class="bi bi-building me-2 text-primary"></i>Business</h5>
                        <p class="mb-0"><?php echo BUSINESS_NAME; ?></p>
                    </div>
                    
                    <div class="contact-item mb-3">
                        <h5><i class="bi bi-geo-alt me-2 text-primary"></i>Address</h5>
                        <p class="mb-0">
                            <?php echo BUSINESS_ADDRESS; ?><br>
                            <?php echo BUSINESS_DESCRIPTION; ?><br>
                            <?php echo BUSINESS_CITY; ?>, <?php echo BUSINESS_STATE; ?> <?php echo BUSINESS_ZIP; ?>
                        </p>
                    </div>
                    
                    <div class="contact-item mb-3">
                        <h5><i class="bi bi-telephone me-2 text-primary"></i>Phone</h5>
                        <p class="mb-0">
                            <a href="tel:<?php echo SITE_PHONE; ?>" class="text-decoration-none">
                                <?php echo formatPhone(SITE_PHONE); ?>
                            </a>
                        </p>
                    </div>
                    
                    <div class="contact-item mb-3">
                        <h5><i class="bi bi-envelope me-2 text-primary"></i>Email</h5>
                        <p class="mb-0">
                            <a href="mailto:<?php echo SITE_EMAIL; ?>" class="text-decoration-none">
                                <?php echo SITE_EMAIL; ?>
                            </a>
                        </p>
                    </div>
                    
                    <div class="contact-item mb-3">
                        <h5><i class="bi bi-clock me-2 text-primary"></i>Hours</h5>
                        <div class="hours-list">
                            <?php foreach ($contactInfo['hours'] as $day => $hours): ?>
                                <div class="d-flex justify-content-between">
                                    <span><?php echo ucfirst($day); ?>:</span>
                                    <span><?php echo $hours; ?></span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <?php if ($contactInfo['is_open']): ?>
                            <span class="badge bg-success mt-2">Currently Open</span>
                        <?php else: ?>
                            <span class="badge bg-secondary mt-2">Currently Closed</span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="contact-item">
                        <h5><i class="bi bi-facebook me-2 text-primary"></i>Social Media</h5>
                        <a href="<?php echo FACEBOOK_URL; ?>" 
                           class="btn btn-outline-primary btn-sm" 
                           target="_blank" 
                           rel="noopener">
                            <i class="bi bi-facebook me-1"></i>Follow Us
                        </a>
                    </div>
                </div>
                
                <!-- Special Features -->
                <div class="special-features mt-4 p-4 bg-success text-white rounded">
                    <h4>Our Services</h4>
                    <div class="row">
                        <div class="col-6">
                            <ul class="list-unstyled mb-0">
                                <li><i class="bi bi-check-circle me-2"></i>Gift Cards Available</li>
                                <li><i class="bi bi-check-circle me-2"></i>Delivery Service</li>
                                <li><i class="bi bi-check-circle me-2"></i>Bulk Materials</li>
                            </ul>
                        </div>
                        <div class="col-6">
                            <ul class="list-unstyled mb-0">
                                <li><i class="bi bi-check-circle me-2"></i>Equipment Sales</li>
                                <li><i class="bi bi-check-circle me-2"></i>Expert Advice</li>
                                <li><i class="bi bi-check-circle me-2"></i>Custom Orders</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Quick Quote Request -->
                <div class="quick-quote mt-4 p-4 bg-warning text-dark rounded">
                    <h5><i class="bi bi-calculator me-2"></i>Need a Quick Quote?</h5>
                    <p class="mb-3">Call us with your project details for an instant estimate!</p>
                    <a href="tel:<?php echo SITE_PHONE; ?>" class="btn btn-dark">
                        <i class="bi bi-telephone me-2"></i>Get Quote Now
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="map-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-4">
                <h2>Find Us</h2>
                <p class="lead">Located at the corner of Monroe, 1/2 mile East of Telegraph</p>
            </div>
        </div>
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="map-container rounded shadow">
                    <img src="<?php echo IMAGES_URL; ?>/Map.jpg" 
                         alt="D&L Garden Center Location Map" 
                         class="img-fluid rounded">
                </div>
                <div class="text-center mt-3">
                    <a href="https://maps.google.com/?q=21980+Ecorse+Rd+Taylor+MI+48180" 
                       class="btn btn-primary" 
                       target="_blank" 
                       rel="noopener">
                        <i class="bi bi-geo-alt me-2"></i>Get Directions
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<?php include '../includes/footer.php'; ?>
