<?php
/**
 * D&L Garden Center - Gallery Page
 * Photo gallery with Fancybox integration
 */

define('DL_GARDEN_CENTER', true);
require_once '../includes/config.php';

// Page meta data
$pageTitle = 'Photo Gallery - ' . SITE_NAME;
$pageDescription = 'Browse photos of our products, facilities, and customer projects at D&L Garden Center in Taylor, MI.';
$pageKeywords = 'photo gallery, products, facilities, landscaping projects, garden center photos';

// Get gallery images from database or use sample data
try {
    $galleryImages = $galleryManager->getImages(['limit' => 20]);

    // If no database images, use sample data
    if (empty($galleryImages)) {
        $galleryImages = [
            [
                'title' => 'Garden Center Facility',
                'filename' => 'head_image.jpg',
                'caption' => 'Our main facility on Ecorse Road - serving the community since 1978',
                'category' => 'Facilities',
                'category_name' => 'Facilities'
            ],
            [
                'title' => 'Stihl Equipment Display',
                'filename' => 'Stihl-logo-e1519758287975.png',
                'caption' => 'Authorized Stihl dealer with full product line including chainsaws, trimmers, and blowers',
                'category' => 'Equipment',
                'category_name' => 'Equipment'
            ],
            [
                'title' => 'Toro Equipment',
                'filename' => 'Toro-300x189.png',
                'caption' => 'Quality Toro lawn and garden equipment for homeowners and professionals',
                'category' => 'Equipment',
                'category_name' => 'Equipment'
            ],
            [
                'title' => 'Location Map',
                'filename' => 'Map.jpg',
                'caption' => 'Easy to find location at 21980 Ecorse Rd, corner of Monroe',
                'category' => 'Facilities',
                'category_name' => 'Facilities'
            ],
            [
                'title' => 'Special Offers',
                'filename' => 'coupon-1-212x300.png',
                'caption' => 'Regular coupons and special offers for our customers',
                'category' => 'Promotions',
                'category_name' => 'Promotions'
            ],
            [
                'title' => 'Online Coupons',
                'filename' => 'online_coupons2.gif',
                'caption' => 'Printable online coupons available on our website',
                'category' => 'Promotions',
                'category_name' => 'Promotions'
            ]
        ];
    }
} catch (Exception $e) {
    // Fallback to sample data if database error
    $galleryImages = [
        [
            'title' => 'Garden Center Facility',
            'filename' => 'head_image.jpg',
            'caption' => 'Our main facility on Ecorse Road',
            'category' => 'Facilities',
            'category_name' => 'Facilities'
        ]
    ];
}

include '../includes/header.php';
?>

<!-- Page Header -->
<section class="page-header bg-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1>Photo Gallery</h1>
                <p class="lead">See our products, facilities, and customer projects</p>
            </div>
        </div>
    </div>
</section>

<!-- Gallery Section -->
<section class="gallery-section py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2>Browse Our Gallery</h2>
                <p class="lead">Click on any image to view in full size</p>
            </div>
        </div>
        
        <!-- Gallery Categories -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <div class="btn-group flex-wrap" role="group" aria-label="Gallery categories">
                    <button type="button" class="btn btn-outline-primary active" data-filter="all">
                        <i class="bi bi-grid me-1"></i>All Photos
                    </button>
                    <button type="button" class="btn btn-outline-primary" data-filter="facilities">
                        <i class="bi bi-building me-1"></i>Facilities
                    </button>
                    <button type="button" class="btn btn-outline-primary" data-filter="equipment">
                        <i class="bi bi-tools me-1"></i>Equipment
                    </button>
                    <button type="button" class="btn btn-outline-primary" data-filter="products">
                        <i class="bi bi-box me-1"></i>Products
                    </button>
                    <button type="button" class="btn btn-outline-primary" data-filter="projects">
                        <i class="bi bi-hammer me-1"></i>Projects
                    </button>
                    <button type="button" class="btn btn-outline-primary" data-filter="promotions">
                        <i class="bi bi-tag me-1"></i>Promotions
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Gallery Grid -->
        <div class="gallery-grid">
            <?php foreach ($galleryImages as $index => $image): ?>
                <?php
                $imageUrl = isset($image['url']) ? $image['url'] : IMAGES_URL . '/' . $image['filename'];
                $categorySlug = strtolower($image['category_name'] ?? $image['category'] ?? 'general');
                ?>
                <div class="gallery-item" data-category="<?php echo $categorySlug; ?>">
                    <a href="<?php echo $imageUrl; ?>"
                       data-fancybox="gallery"
                       data-caption="<?php echo htmlspecialchars($image['caption'] ?? $image['title']); ?>"
                       data-thumb="<?php echo $imageUrl; ?>">
                        <img src="<?php echo $imageUrl; ?>"
                             alt="<?php echo htmlspecialchars($image['title']); ?>"
                             class="img-fluid"
                             loading="lazy">
                        <div class="gallery-overlay">
                            <div class="overlay-content">
                                <i class="bi bi-zoom-in text-white fs-2 mb-2"></i>
                                <p class="text-white mb-0 small">Click to view</p>
                            </div>
                        </div>
                    </a>
                    <div class="gallery-info mt-2">
                        <h6 class="mb-1"><?php echo htmlspecialchars($image['title']); ?></h6>
                        <small class="text-muted"><?php echo htmlspecialchars($image['category_name'] ?? $image['category'] ?? 'General'); ?></small>
                    </div>
                </div>
            <?php endforeach; ?>
            
            <!-- Placeholder items for demonstration -->
            <div class="gallery-item" data-category="products">
                <div class="placeholder-item bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                    <div class="text-center text-muted">
                        <i class="bi bi-image fs-1"></i>
                        <p class="mt-2">Bulk Materials</p>
                    </div>
                </div>
            </div>
            
            <div class="gallery-item" data-category="products">
                <div class="placeholder-item bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                    <div class="text-center text-muted">
                        <i class="bi bi-image fs-1"></i>
                        <p class="mt-2">Mulch Display</p>
                    </div>
                </div>
            </div>
            
            <div class="gallery-item" data-category="projects">
                <div class="placeholder-item bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                    <div class="text-center text-muted">
                        <i class="bi bi-image fs-1"></i>
                        <p class="mt-2">Customer Project</p>
                    </div>
                </div>
            </div>
            
            <div class="gallery-item" data-category="facilities">
                <div class="placeholder-item bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                    <div class="text-center text-muted">
                        <i class="bi bi-image fs-1"></i>
                        <p class="mt-2">Storage Area</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Upload Notice -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <h5><i class="bi bi-camera me-2"></i>Share Your Projects</h5>
                    <p class="mb-0">Have a project using our materials? We'd love to see it! 
                    <a href="/contact/" class="alert-link">Contact us</a> to share your photos.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Additional JavaScript for Gallery Filtering -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gallery filtering
    const filterButtons = document.querySelectorAll('[data-filter]');
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter gallery items
            galleryItems.forEach(item => {
                const category = item.getAttribute('data-category');
                
                if (filter === 'all' || category === filter) {
                    item.style.display = 'block';
                    item.classList.add('fade-in');
                } else {
                    item.style.display = 'none';
                    item.classList.remove('fade-in');
                }
            });
        });
    });
});
</script>

<!-- Additional CSS for Gallery -->
<style>
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.gallery-item {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.gallery-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.8), rgba(13, 110, 253, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    transform: translateY(10px);
    transition: var(--transition);
}

.gallery-item:hover .overlay-content {
    transform: translateY(0);
}

.gallery-info h6 {
    font-weight: 600;
    color: var(--text-dark);
}

.btn-group .btn {
    margin: 0.25rem;
}

@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin: 0.125rem 0;
        border-radius: var(--border-radius) !important;
    }
}

.placeholder-item {
    border: 2px dashed #dee2e6;
    border-radius: var(--border-radius);
}

@media (max-width: 768px) {
    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 576px) {
    .gallery-grid {
        grid-template-columns: 1fr 1fr;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
