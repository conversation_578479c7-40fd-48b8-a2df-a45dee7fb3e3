<?php
/**
 * D&L Garden Center - Gallery Page
 * Photo gallery with Fancybox integration
 */

define('DL_GARDEN_CENTER', true);
require_once '../includes/config.php';

// Page meta data
$pageTitle = 'Photo Gallery - ' . SITE_NAME;
$pageDescription = 'Browse photos of our products, facilities, and customer projects at D&L Garden Center in Taylor, MI.';
$pageKeywords = 'photo gallery, products, facilities, landscaping projects, garden center photos';

// Sample gallery data (in a real implementation, this would come from the database)
$galleryImages = [
    [
        'title' => 'Garden Center Facility',
        'filename' => 'head_image.jpg',
        'caption' => 'Our main facility on Ecorse Road',
        'category' => 'Facilities'
    ],
    [
        'title' => 'Stihl Equipment',
        'filename' => 'Stihl-logo-e1519758287975.png',
        'caption' => 'Authorized Stihl dealer with full product line',
        'category' => 'Equipment'
    ],
    [
        'title' => 'Toro Equipment',
        'filename' => 'Toro-300x189.png',
        'caption' => 'Quality Toro lawn and garden equipment',
        'category' => 'Equipment'
    ]
];

include '../includes/header.php';
?>

<!-- Page Header -->
<section class="page-header bg-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1>Photo Gallery</h1>
                <p class="lead">See our products, facilities, and customer projects</p>
            </div>
        </div>
    </div>
</section>

<!-- Gallery Section -->
<section class="gallery-section py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2>Browse Our Gallery</h2>
                <p class="lead">Click on any image to view in full size</p>
            </div>
        </div>
        
        <!-- Gallery Categories -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <div class="btn-group" role="group" aria-label="Gallery categories">
                    <button type="button" class="btn btn-outline-primary active" data-filter="all">All Photos</button>
                    <button type="button" class="btn btn-outline-primary" data-filter="facilities">Facilities</button>
                    <button type="button" class="btn btn-outline-primary" data-filter="equipment">Equipment</button>
                    <button type="button" class="btn btn-outline-primary" data-filter="products">Products</button>
                    <button type="button" class="btn btn-outline-primary" data-filter="projects">Projects</button>
                </div>
            </div>
        </div>
        
        <!-- Gallery Grid -->
        <div class="gallery-grid">
            <?php foreach ($galleryImages as $index => $image): ?>
                <div class="gallery-item" data-category="<?php echo strtolower($image['category']); ?>">
                    <a href="<?php echo IMAGES_URL . '/' . $image['filename']; ?>" 
                       data-fancybox="gallery" 
                       data-caption="<?php echo htmlspecialchars($image['caption']); ?>">
                        <img src="<?php echo IMAGES_URL . '/' . $image['filename']; ?>" 
                             alt="<?php echo htmlspecialchars($image['title']); ?>" 
                             class="img-fluid">
                        <div class="gallery-overlay">
                            <i class="bi bi-zoom-in text-white fs-2"></i>
                        </div>
                    </a>
                    <div class="gallery-info mt-2">
                        <h6><?php echo htmlspecialchars($image['title']); ?></h6>
                        <small class="text-muted"><?php echo htmlspecialchars($image['category']); ?></small>
                    </div>
                </div>
            <?php endforeach; ?>
            
            <!-- Placeholder items for demonstration -->
            <div class="gallery-item" data-category="products">
                <div class="placeholder-item bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                    <div class="text-center text-muted">
                        <i class="bi bi-image fs-1"></i>
                        <p class="mt-2">Bulk Materials</p>
                    </div>
                </div>
            </div>
            
            <div class="gallery-item" data-category="products">
                <div class="placeholder-item bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                    <div class="text-center text-muted">
                        <i class="bi bi-image fs-1"></i>
                        <p class="mt-2">Mulch Display</p>
                    </div>
                </div>
            </div>
            
            <div class="gallery-item" data-category="projects">
                <div class="placeholder-item bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                    <div class="text-center text-muted">
                        <i class="bi bi-image fs-1"></i>
                        <p class="mt-2">Customer Project</p>
                    </div>
                </div>
            </div>
            
            <div class="gallery-item" data-category="facilities">
                <div class="placeholder-item bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                    <div class="text-center text-muted">
                        <i class="bi bi-image fs-1"></i>
                        <p class="mt-2">Storage Area</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Upload Notice -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <h5><i class="bi bi-camera me-2"></i>Share Your Projects</h5>
                    <p class="mb-0">Have a project using our materials? We'd love to see it! 
                    <a href="/contact/" class="alert-link">Contact us</a> to share your photos.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Additional JavaScript for Gallery Filtering -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gallery filtering
    const filterButtons = document.querySelectorAll('[data-filter]');
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter gallery items
            galleryItems.forEach(item => {
                const category = item.getAttribute('data-category');
                
                if (filter === 'all' || category === filter) {
                    item.style.display = 'block';
                    item.classList.add('fade-in');
                } else {
                    item.style.display = 'none';
                    item.classList.remove('fade-in');
                }
            });
        });
    });
});
</script>

<!-- Additional CSS for Gallery -->
<style>
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.gallery-item {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.gallery-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.placeholder-item {
    border: 2px dashed #dee2e6;
    border-radius: var(--border-radius);
}

@media (max-width: 768px) {
    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 576px) {
    .gallery-grid {
        grid-template-columns: 1fr 1fr;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
