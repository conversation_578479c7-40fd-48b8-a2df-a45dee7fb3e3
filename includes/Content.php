<?php
/**
 * D&L Garden Center - Content Management Class
 * Handles dynamic content blocks and settings
 */

if (!defined('DL_GARDEN_CENTER')) {
    die('Direct access not permitted');
}

class Content {
    private $db;
    private $cache = [];
    
    public function __construct(Database $database) {
        $this->db = $database;
    }
    
    /**
     * Get content block
     */
    public function getBlock($page, $section) {
        $cacheKey = "{$page}_{$section}";

        if (isset($this->cache[$cacheKey])) {
            return $this->cache[$cacheKey];
        }

        try {
            $sql = "
                SELECT *
                FROM content_blocks
                WHERE page = :page AND section = :section AND is_active = 1
                ORDER BY sort_order ASC
            ";

            $blocks = $this->db->fetchAll($sql, [
                'page' => $page,
                'section' => $section
            ]);

            $this->cache[$cacheKey] = $blocks;
            return $blocks;

        } catch (Exception $e) {
            // Table doesn't exist yet - return empty array
            $this->cache[$cacheKey] = [];
            return [];
        }
    }
    
    /**
     * Get single content block
     */
    public function getSingleBlock($page, $section) {
        $blocks = $this->getBlock($page, $section);
        return !empty($blocks) ? $blocks[0] : null;
    }
    
    /**
     * Render content block
     */
    public function renderBlock($page, $section, $default = '') {
        $block = $this->getSingleBlock($page, $section);
        
        if (!$block) {
            return $default;
        }
        
        $content = $block['content'];
        
        // Process content based on type
        switch ($block['content_type']) {
            case 'markdown':
                // Simple markdown processing (you might want to use a proper markdown parser)
                $content = $this->processMarkdown($content);
                break;
            case 'text':
                $content = nl2br(htmlspecialchars($content));
                break;
            case 'html':
            default:
                // HTML content - already processed
                break;
        }
        
        return $content;
    }
    
    /**
     * Get setting value
     */
    public function getSetting($key, $default = null) {
        if (isset($this->cache["setting_{$key}"])) {
            return $this->cache["setting_{$key}"];
        }

        try {
            $sql = "
                SELECT setting_value, setting_type
                FROM settings
                WHERE setting_key = :key
            ";

            $result = $this->db->fetchOne($sql, ['key' => $key]);

            if (!$result) {
                $this->cache["setting_{$key}"] = $default;
                return $default;
            }

            $value = $this->convertSettingValue($result['setting_value'], $result['setting_type']);
            $this->cache["setting_{$key}"] = $value;

            return $value;

        } catch (Exception $e) {
            // Table doesn't exist yet - return default
            $this->cache["setting_{$key}"] = $default;
            return $default;
        }
    }
    
    /**
     * Get multiple settings
     */
    public function getSettings($keys = []) {
        if (empty($keys)) {
            // Get all public settings
            $sql = "
                SELECT setting_key, setting_value, setting_type
                FROM settings
                WHERE is_public = 1
            ";
            $results = $this->db->fetchAll($sql);
        } else {
            $placeholders = str_repeat('?,', count($keys) - 1) . '?';
            $sql = "
                SELECT setting_key, setting_value, setting_type
                FROM settings
                WHERE setting_key IN ($placeholders)
            ";
            $results = $this->db->fetchAll($sql, $keys);
        }
        
        $settings = [];
        foreach ($results as $result) {
            $settings[$result['setting_key']] = $this->convertSettingValue(
                $result['setting_value'],
                $result['setting_type']
            );
        }
        
        return $settings;
    }
    
    /**
     * Convert setting value based on type
     */
    private function convertSettingValue($value, $type) {
        switch ($type) {
            case 'number':
                return is_numeric($value) ? (float)$value : 0;
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'json':
                return json_decode($value, true) ?: [];
            case 'string':
            default:
                return $value;
        }
    }
    
    /**
     * Simple markdown processing
     */
    private function processMarkdown($text) {
        // Basic markdown processing - you might want to use a proper library
        $text = htmlspecialchars($text);
        
        // Headers
        $text = preg_replace('/^### (.+)$/m', '<h3>$1</h3>', $text);
        $text = preg_replace('/^## (.+)$/m', '<h2>$1</h2>', $text);
        $text = preg_replace('/^# (.+)$/m', '<h1>$1</h1>', $text);
        
        // Bold and italic
        $text = preg_replace('/\*\*(.+?)\*\*/', '<strong>$1</strong>', $text);
        $text = preg_replace('/\*(.+?)\*/', '<em>$1</em>', $text);
        
        // Links
        $text = preg_replace('/\[(.+?)\]\((.+?)\)/', '<a href="$2">$1</a>', $text);
        
        // Line breaks
        $text = nl2br($text);
        
        return $text;
    }
    
    /**
     * Get business hours
     */
    public function getBusinessHours() {
        $hours = $this->getSetting('business_hours', []);
        
        if (empty($hours)) {
            // Default hours
            return [
                'monday' => '8:00 AM - 6:00 PM',
                'tuesday' => '8:00 AM - 6:00 PM',
                'wednesday' => '8:00 AM - 6:00 PM',
                'thursday' => '8:00 AM - 6:00 PM',
                'friday' => '8:00 AM - 6:00 PM',
                'saturday' => '8:00 AM - 6:00 PM',
                'sunday' => '9:00 AM - 5:00 PM'
            ];
        }
        
        return $hours;
    }
    
    /**
     * Check if currently open
     */
    public function isCurrentlyOpen() {
        $hours = $this->getBusinessHours();
        $currentDay = strtolower(date('l'));
        $currentTime = date('H:i');
        
        if (!isset($hours[$currentDay])) {
            return false;
        }
        
        $dayHours = $hours[$currentDay];
        
        // Parse hours (assumes format like "8:00 AM - 6:00 PM")
        if (preg_match('/(\d{1,2}:\d{2}\s*[AP]M)\s*-\s*(\d{1,2}:\d{2}\s*[AP]M)/i', $dayHours, $matches)) {
            $openTime = date('H:i', strtotime($matches[1]));
            $closeTime = date('H:i', strtotime($matches[2]));
            
            return $currentTime >= $openTime && $currentTime <= $closeTime;
        }
        
        return false;
    }
    
    /**
     * Get contact information
     */
    public function getContactInfo() {
        return [
            'business_name' => BUSINESS_NAME,
            'address' => BUSINESS_ADDRESS,
            'city' => BUSINESS_CITY,
            'state' => BUSINESS_STATE,
            'zip' => BUSINESS_ZIP,
            'phone' => SITE_PHONE,
            'email' => SITE_EMAIL,
            'description' => BUSINESS_DESCRIPTION,
            'hours' => $this->getBusinessHours(),
            'is_open' => $this->isCurrentlyOpen()
        ];
    }
    
    /**
     * Save contact form submission
     */
    public function saveContactSubmission($data) {
        $requiredFields = ['name', 'email', 'message'];
        
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                throw new Exception("Required field missing: $field");
            }
        }
        
        // Sanitize data
        $cleanData = [
            'name' => sanitize($data['name']),
            'email' => filter_var($data['email'], FILTER_VALIDATE_EMAIL),
            'phone' => sanitize($data['phone'] ?? ''),
            'subject' => sanitize($data['subject'] ?? ''),
            'message' => sanitize($data['message']),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        if (!$cleanData['email']) {
            throw new Exception("Invalid email address");
        }
        
        return $this->db->insert('contact_submissions', $cleanData);
    }
}
