<?php
/**
 * D&L Garden Center - Gallery Class
 * Handles gallery images and categories
 */

if (!defined('DL_GARDEN_CENTER')) {
    die('Direct access not permitted');
}

class Gallery {
    private $db;
    
    public function __construct(Database $database) {
        $this->db = $database;
    }
    
    /**
     * Get all gallery images with optional filtering
     */
    public function getImages($filters = []) {
        $where = ['1=1'];
        $params = [];
        
        // Category filter
        if (!empty($filters['category_id'])) {
            $where[] = 'gi.category_id = :category_id';
            $params['category_id'] = $filters['category_id'];
        }
        
        // Featured filter
        if (isset($filters['featured']) && $filters['featured']) {
            $where[] = 'gi.is_featured = 1';
        }
        
        // Pagination
        $limit = '';
        if (isset($filters['limit'])) {
            $limit = 'LIMIT ' . (int)$filters['limit'];
            if (isset($filters['offset'])) {
                $limit = 'LIMIT ' . (int)$filters['offset'] . ', ' . (int)$filters['limit'];
            }
        }
        
        $whereClause = implode(' AND ', $where);
        
        $sql = "
            SELECT 
                gi.*,
                gc.name as category_name,
                gc.slug as category_slug
            FROM gallery_images gi
            LEFT JOIN gallery_categories gc ON gi.category_id = gc.id
            WHERE {$whereClause}
            ORDER BY gi.is_featured DESC, gi.sort_order ASC, gi.created_at DESC
            {$limit}
        ";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get single gallery image
     */
    public function getImage($id) {
        $sql = "
            SELECT 
                gi.*,
                gc.name as category_name,
                gc.slug as category_slug
            FROM gallery_images gi
            LEFT JOIN gallery_categories gc ON gi.category_id = gc.id
            WHERE gi.id = :id
        ";
        
        return $this->db->fetchOne($sql, ['id' => $id]);
    }
    
    /**
     * Get gallery categories
     */
    public function getCategories() {
        $sql = "
            SELECT 
                gc.*,
                COUNT(gi.id) as image_count
            FROM gallery_categories gc
            LEFT JOIN gallery_images gi ON gc.id = gi.category_id
            WHERE gc.is_active = 1
            GROUP BY gc.id
            ORDER BY gc.sort_order ASC, gc.name ASC
        ";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Get category by slug
     */
    public function getCategory($slug) {
        $sql = "
            SELECT *
            FROM gallery_categories
            WHERE slug = :slug AND is_active = 1
        ";
        
        return $this->db->fetchOne($sql, ['slug' => $slug]);
    }
    
    /**
     * Get images by category
     */
    public function getImagesByCategory($categorySlug, $limit = null) {
        $category = $this->getCategory($categorySlug);
        if (!$category) {
            return [];
        }
        
        $filters = ['category_id' => $category['id']];
        if ($limit) {
            $filters['limit'] = $limit;
        }
        
        return $this->getImages($filters);
    }
    
    /**
     * Get featured images
     */
    public function getFeaturedImages($limit = 12) {
        return $this->getImages([
            'featured' => true,
            'limit' => $limit
        ]);
    }
    
    /**
     * Get recent images
     */
    public function getRecentImages($limit = 12) {
        return $this->getImages(['limit' => $limit]);
    }
    
    /**
     * Format image for display
     */
    public function formatImage($image) {
        if (!$image) return null;
        
        // Add image URLs
        $image['url'] = UPLOADS_URL . '/gallery/' . $image['filename'];
        $image['thumb_url'] = UPLOADS_URL . '/gallery/thumbs/' . $image['filename'];
        
        // Add alt text if empty
        if (empty($image['alt_text'])) {
            $image['alt_text'] = $image['title'] ?: 'Gallery image';
        }
        
        // Format file size
        if ($image['file_size']) {
            $image['formatted_size'] = $this->formatFileSize($image['file_size']);
        }
        
        return $image;
    }
    
    /**
     * Format file size for display
     */
    private function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * Get gallery statistics
     */
    public function getStats() {
        $stats = [];
        
        // Total images
        $stats['total_images'] = $this->db->count('gallery_images');
        
        // Images by category
        $sql = "
            SELECT 
                gc.name,
                COUNT(gi.id) as count
            FROM gallery_categories gc
            LEFT JOIN gallery_images gi ON gc.id = gi.category_id
            WHERE gc.is_active = 1
            GROUP BY gc.id
            ORDER BY count DESC
        ";
        $stats['by_category'] = $this->db->fetchAll($sql);
        
        // Recent uploads
        $sql = "
            SELECT COUNT(*) as count
            FROM gallery_images
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ";
        $result = $this->db->fetchOne($sql);
        $stats['recent_uploads'] = $result['count'];
        
        return $stats;
    }
    
    /**
     * Create gallery data for Fancybox
     */
    public function createFancyboxData($images) {
        $fancyboxData = [];
        
        foreach ($images as $image) {
            $formatted = $this->formatImage($image);
            $fancyboxData[] = [
                'src' => $formatted['url'],
                'thumb' => $formatted['thumb_url'],
                'caption' => $formatted['caption'] ?: $formatted['title'],
                'alt' => $formatted['alt_text']
            ];
        }
        
        return $fancyboxData;
    }
}
