<?php
/**
 * D&L Garden Center - Product Class
 * Handles product data and operations
 */

if (!defined('DL_GARDEN_CENTER')) {
    die('Direct access not permitted');
}

class Product {
    private $db;
    
    public function __construct(Database $database) {
        $this->db = $database;
    }
    
    /**
     * Get all products with optional filtering
     */
    public function getProducts($filters = []) {
        try {
            $where = ['p.is_active = 1'];
            $params = [];

            // Category filter
            if (!empty($filters['category_id'])) {
                $where[] = 'p.category_id = :category_id';
                $params['category_id'] = $filters['category_id'];
            }

            // Product type filter
            if (!empty($filters['product_type'])) {
                $where[] = 'p.product_type = :product_type';
                $params['product_type'] = $filters['product_type'];
            }

            // Search filter
            if (!empty($filters['search'])) {
                $where[] = '(p.name LIKE :search OR p.description LIKE :search)';
                $params['search'] = '%' . $filters['search'] . '%';
            }

            // Featured filter
            if (isset($filters['featured']) && $filters['featured']) {
                $where[] = 'p.is_featured = 1';
            }

            // Pagination
            $limit = '';
            if (isset($filters['limit'])) {
                $limit = 'LIMIT ' . (int)$filters['limit'];
                if (isset($filters['offset'])) {
                    $limit = 'LIMIT ' . (int)$filters['offset'] . ', ' . (int)$filters['limit'];
                }
            }

            $whereClause = implode(' AND ', $where);

            $sql = "
                SELECT
                    p.*,
                    c.name as category_name,
                    c.slug as category_slug,
                    MIN(pr.pickup_price) as min_pickup_price,
                    MIN(pr.delivery_price) as min_delivery_price,
                    pi.filename as primary_image
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN product_pricing pr ON p.id = pr.product_id AND pr.is_active = 1
                LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
                WHERE {$whereClause}
                GROUP BY p.id
                ORDER BY p.name ASC
                {$limit}
            ";

            return $this->db->fetchAll($sql, $params);

        } catch (Exception $e) {
            // Tables don't exist yet - return empty array
            return [];
        }
    }
    
    /**
     * Get single product by ID or slug
     */
    public function getProduct($identifier, $bySlug = false) {
        $field = $bySlug ? 'p.slug' : 'p.id';
        
        $sql = "
            SELECT 
                p.*,
                c.name as category_name,
                c.slug as category_slug
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE {$field} = :identifier AND p.is_active = 1
        ";
        
        return $this->db->fetchOne($sql, ['identifier' => $identifier]);
    }
    
    /**
     * Get product pricing
     */
    public function getProductPricing($productId) {
        $sql = "
            SELECT *
            FROM product_pricing
            WHERE product_id = :product_id AND is_active = 1
            ORDER BY quantity_min ASC
        ";
        
        return $this->db->fetchAll($sql, ['product_id' => $productId]);
    }
    
    /**
     * Get product images
     */
    public function getProductImages($productId) {
        $sql = "
            SELECT *
            FROM product_images
            WHERE product_id = :product_id
            ORDER BY is_primary DESC, sort_order ASC
        ";
        
        return $this->db->fetchAll($sql, ['product_id' => $productId]);
    }
    
    /**
     * Get featured products
     */
    public function getFeaturedProducts($limit = 6) {
        return $this->getProducts([
            'featured' => true,
            'limit' => $limit
        ]);
    }
    
    /**
     * Get products by category
     */
    public function getProductsByCategory($categorySlug, $limit = null) {
        $category = $this->getCategory($categorySlug, true);
        if (!$category) {
            return [];
        }
        
        $filters = ['category_id' => $category['id']];
        if ($limit) {
            $filters['limit'] = $limit;
        }
        
        return $this->getProducts($filters);
    }
    
    /**
     * Get category by ID or slug
     */
    public function getCategory($identifier, $bySlug = false) {
        $field = $bySlug ? 'slug' : 'id';
        
        $sql = "
            SELECT *
            FROM categories
            WHERE {$field} = :identifier AND is_active = 1
        ";
        
        return $this->db->fetchOne($sql, ['identifier' => $identifier]);
    }
    
    /**
     * Get all categories
     */
    public function getCategories($parentId = null) {
        $where = 'is_active = 1';
        $params = [];
        
        if ($parentId !== null) {
            $where .= ' AND parent_id = :parent_id';
            $params['parent_id'] = $parentId;
        } else {
            $where .= ' AND parent_id IS NULL';
        }
        
        $sql = "
            SELECT *
            FROM categories
            WHERE {$where}
            ORDER BY sort_order ASC, name ASC
        ";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Search products
     */
    public function searchProducts($query, $limit = 20) {
        return $this->getProducts([
            'search' => $query,
            'limit' => $limit
        ]);
    }
    
    /**
     * Get product count
     */
    public function getProductCount($filters = []) {
        $where = ['is_active = 1'];
        $params = [];
        
        if (!empty($filters['category_id'])) {
            $where[] = 'category_id = :category_id';
            $params['category_id'] = $filters['category_id'];
        }
        
        if (!empty($filters['product_type'])) {
            $where[] = 'product_type = :product_type';
            $params['product_type'] = $filters['product_type'];
        }
        
        if (!empty($filters['search'])) {
            $where[] = '(name LIKE :search OR description LIKE :search)';
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        $whereClause = implode(' AND ', $where);
        
        return $this->db->count('products', $whereClause, $params);
    }
    
    /**
     * Format product for display
     */
    public function formatProduct($product) {
        if (!$product) return null;
        
        // Add formatted prices
        if (isset($product['min_pickup_price'])) {
            $product['formatted_pickup_price'] = formatPrice($product['min_pickup_price']);
        }
        
        if (isset($product['min_delivery_price'])) {
            $product['formatted_delivery_price'] = formatPrice($product['min_delivery_price']);
        }
        
        // Add image URL
        if (!empty($product['primary_image'])) {
            $product['image_url'] = IMAGES_URL . '/' . $product['primary_image'];
        } else {
            $product['image_url'] = IMAGES_URL . '/placeholder.jpg';
        }
        
        // Add product URL
        $product['url'] = getPageUrl('products/' . $product['category_slug'], $product['slug']);
        
        return $product;
    }
}
