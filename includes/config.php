<?php
/**
 * D&L Garden Center - Configuration File
 * Modern PHP configuration for the redesigned website
 */

// Prevent direct access
if (!defined('DL_GARDEN_CENTER')) {
    define('DL_GARDEN_CENTER', true);
}

// Error reporting for development (change for production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Site Configuration
define('SITE_NAME', 'D&L Garden Center');
define('SITE_TAGLINE', 'Your Complete Garden Center Since 1978');
define('SITE_URL', 'http://localhost'); // Change for production
define('SITE_EMAIL', '<EMAIL>');
define('SITE_PHONE', '************');

// Business Information
define('BUSINESS_NAME', 'D&L Garden Center Inc.');
define('BUSINESS_ADDRESS', '21980 Ecorse Rd.');
define('BUSINESS_CITY', 'Taylor');
define('BUSINESS_STATE', 'MI');
define('BUSINESS_ZIP', '48180');
define('BUSINESS_DESCRIPTION', 'Corner of Monroe 1/2 mile East of Telegraph');

// Database Configuration (MariaDB)
define('DB_HOST', 'localhost');
define('DB_NAME', 'dlgarden_db');
define('DB_USER', 'dlgarden_user');
define('DB_PASS', 'dlgarden_pass_2025!');
define('DB_CHARSET', 'utf8mb4');

// File Paths
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('UPLOADS_PATH', ROOT_PATH . '/assets/uploads');
define('IMAGES_PATH', ROOT_PATH . '/assets/images');

// URL Paths
define('ASSETS_URL', SITE_URL . '/assets');
define('UPLOADS_URL', SITE_URL . '/assets/uploads');
define('IMAGES_URL', SITE_URL . '/assets/images');

// Security
define('ADMIN_SESSION_NAME', 'dlgarden_admin');
define('CSRF_TOKEN_NAME', 'dlgarden_csrf');
session_start();

// Timezone
date_default_timezone_set('America/Detroit');

// Social Media
define('FACEBOOK_URL', 'http://www.facebook.com/dlgardencenter');
define('STIHL_DEALER_URL', 'https://dlgarden.stihldealer.net/');

// Image Upload Settings
define('MAX_UPLOAD_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// Pagination
define('PRODUCTS_PER_PAGE', 12);
define('GALLERY_IMAGES_PER_PAGE', 20);

// Cache Settings
define('CACHE_ENABLED', false); // Enable for production
define('CACHE_DURATION', 3600); // 1 hour

// Development/Production Mode
define('DEVELOPMENT_MODE', true); // Set to false for production

// Auto-load required files
require_once INCLUDES_PATH . '/functions.php';
require_once INCLUDES_PATH . '/database.php';
require_once INCLUDES_PATH . '/Product.php';
require_once INCLUDES_PATH . '/Gallery.php';
require_once INCLUDES_PATH . '/Content.php';

// Initialize database connection and core classes
try {
    $db = new Database();
    $pdo = $db->getConnection();

    // Initialize core classes
    $productManager = new Product($db);
    $galleryManager = new Gallery($db);
    $contentManager = new Content($db);

} catch (Exception $e) {
    if (DEVELOPMENT_MODE) {
        die('Database connection failed: ' . $e->getMessage());
    } else {
        die('Website temporarily unavailable. Please try again later.');
    }
}
