<?php
if (!defined('DL_GARDEN_CENTER')) {
    define('DL_GARDEN_CENTER', true);
    require_once __DIR__ . '/config.php';
}

$pageTitle = $pageTitle ?? SITE_NAME;
$pageDescription = $pageDescription ?? 'Complete garden center in Taylor, MI. Bulk materials, plants, equipment, and landscaping supplies since 1978.';
$pageKeywords = $pageKeywords ?? 'garden center, landscaping, bulk materials, mulch, topsoil, plants, Taylor Michigan';
$currentPath = getCurrentPath();
$navigation = getNavigationMenu();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($pageKeywords); ?>">
    <meta name="author" content="<?php echo BUSINESS_NAME; ?>">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL . $currentPath; ?>">
    <meta property="og:title" content="<?php echo htmlspecialchars($pageTitle); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta property="og:image" content="<?php echo IMAGES_URL; ?>/head_image.jpg">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php echo SITE_URL . $currentPath; ?>">
    <meta property="twitter:title" content="<?php echo htmlspecialchars($pageTitle); ?>">
    <meta property="twitter:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta property="twitter:image" content="<?php echo IMAGES_URL; ?>/head_image.jpg">
    
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>/images/favicon.ico">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    
    <!-- Fancybox CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/style.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        "name": "<?php echo BUSINESS_NAME; ?>",
        "image": "<?php echo IMAGES_URL; ?>/head_image.jpg",
        "description": "<?php echo htmlspecialchars($pageDescription); ?>",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "<?php echo BUSINESS_ADDRESS; ?>",
            "addressLocality": "<?php echo BUSINESS_CITY; ?>",
            "addressRegion": "<?php echo BUSINESS_STATE; ?>",
            "postalCode": "<?php echo BUSINESS_ZIP; ?>"
        },
        "telephone": "<?php echo SITE_PHONE; ?>",
        "url": "<?php echo SITE_URL; ?>",
        "sameAs": [
            "<?php echo FACEBOOK_URL; ?>"
        ],
        "openingHours": "Mo-Su 08:00-18:00",
        "priceRange": "$$"
    }
    </script>
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a class="visually-hidden-focusable" href="#main-content">Skip to main content</a>
    
    <!-- Header -->
    <header class="site-header">
        <!-- Top Bar -->
        <div class="top-bar bg-success text-white py-2">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-telephone me-2"></i>
                            <span><?php echo formatPhone(SITE_PHONE); ?></span>
                            <span class="mx-3">|</span>
                            <i class="bi bi-clock me-2"></i>
                            <span>Open Daily - Call for Hours</span>
                        </div>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="d-flex justify-content-md-end align-items-center">
                            <a href="<?php echo FACEBOOK_URL; ?>" class="text-white me-3" target="_blank" rel="noopener">
                                <i class="bi bi-facebook"></i>
                            </a>
                            <span class="badge bg-warning text-dark">Gift Cards Available</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Header -->
        <div class="main-header bg-white shadow-sm">
            <div class="container">
                <nav class="navbar navbar-expand-lg navbar-light py-3">
                    <!-- Logo -->
                    <a class="navbar-brand" href="/">
                        <img src="<?php echo IMAGES_URL; ?>/head_image.jpg" alt="<?php echo SITE_NAME; ?>" class="logo-img">
                    </a>
                    
                    <!-- Mobile menu toggle -->
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    
                    <!-- Navigation Menu -->
                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav ms-auto">
                            <?php foreach ($navigation as $key => $item): ?>
                                <li class="nav-item <?php echo isset($item['submenu']) ? 'dropdown' : ''; ?>">
                                    <?php if (isset($item['submenu'])): ?>
                                        <a class="nav-link dropdown-toggle <?php echo $item['active'] ? 'active' : ''; ?>" 
                                           href="<?php echo $item['url']; ?>" 
                                           id="navbarDropdown<?php echo ucfirst($key); ?>" 
                                           role="button" 
                                           data-bs-toggle="dropdown">
                                            <?php echo $item['title']; ?>
                                        </a>
                                        <ul class="dropdown-menu">
                                            <?php foreach ($item['submenu'] as $subitem): ?>
                                                <li><a class="dropdown-item" href="<?php echo $subitem['url']; ?>"><?php echo $subitem['title']; ?></a></li>
                                            <?php endforeach; ?>
                                        </ul>
                                    <?php else: ?>
                                        <a class="nav-link <?php echo $item['active'] ? 'active' : ''; ?>" 
                                           href="<?php echo $item['url']; ?>">
                                            <?php echo $item['title']; ?>
                                        </a>
                                    <?php endif; ?>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </nav>
            </div>
        </div>
    </header>
    
    <!-- Main Content -->
    <main id="main-content" class="main-content">
