/*
Theme Name: SuperSimple
Theme URI: http://tidythemes.com/supersimple/
Author: TidyThemes
Author URI: http://tidythemes.com/
Description: A super simple and clean responsive CSS framework built on top of <a href="http://wordpress.org/themes/blankslate">BlankSlate</a>. <a href="http://tidythemes.com/want-to-learn-css/">Learn CSS</a> | <a href="http://tidythemes.com/forum/">Support Forum</a>
Version: 4.0.1
License: GNU General Public License
License URI: https://www.gnu.org/licenses/gpl.html
Tags: black, blue, gray, silver, white, light, one-column, two-columns, left-sidebar, right-sidebar, fixed-layout, fluid-layout, responsive-layout, custom-menu, featured-images, full-width-template, microformats, post-formats, sticky-post, threaded-comments, translation-ready
Text Domain: supersimple

SuperSimple WordPress Theme © 2011-2014 TidyThemes
SuperSimple is distributed under the terms of the GNU GPL
*/

html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, menu, nav, output, ruby, section, summary, time, mark, audio, video {
  font-size: 100%;
  font: inherit;
  padding: 0;
  border: 0;
  margin: 0;
  vertical-align: baseline
}

body {
  line-height: 1
}

ol, ul {
  list-style: none
}

blockquote, q {
  quotes: none
}

blockquote:before, blockquote:after, q:before, q:after {
  content: '';
  content: none
}

table {
  border-collapse: collapse;
  border-spacing: 0
}

article, aside, details, figcaption, figure, footer, header, menu, nav, section {
  display: block
}

.clear {
  clear: both
}

body {
  font-family: arial, helvetica, sans-serif;
  font-size: 14px;
  color: #333;
  margin: 40px 0;
  background: #fff
}

.page-template-creative-php {
  margin: 40px
}

p {
  text-align: justify;
  margin: 20px 0
}

.entry-content p, .entry-content p a {
  font-family: arial, helvetica, sans-serif;
  font-size: 15px;
  line-height: 20px
}

#content {
  line-height: 18px
}

a, h2 {
  font-family: georgia, 'times new roman', serif;
  color: #09f;
  text-decoration: none
}

a:hover {
  text-decoration: underline
}

h1, h2, h3, h4, h5, h6 {
  font-weight: normal;
  margin: 20px 0 10px;
  clear: both
}

h1 {
  font-size: 25px;
  color: #666
}

h2 {
  font-size: 20px;
  line-height: 125%
}

h3 {
  font-size: 16px;
  color: #888
}

strong {
  font-weight: bold
}

em {
  font-style: italic
}

code {
  font-family: 'courier new', courier, serif;
  color: #777
}

pre {
  overflow-x: auto;
  white-space: pre-wrap
}

hr {
  height: 1px;
  color: #ccc;
  border: 0 none;
  margin: 30px 0;
  background-color: #ccc
}

.entry-content ul, .entry-content ol, .comment-content ul, .comment-content ol {
  font-family: helvetica, arial, sans-serif;
  margin-left: 20px !important
}

.entry-content li:before, .comment-content li:before, #hsidebar .children li:before, #fsidebar .children li:before {
  content: "- "
}

ol li:before, #login-register-password li:before {
  content: "" !important
}

#container ol, .comment-content ol, #hsidebar ol, #fsidebar ol {
  list-style: decimal inside
}

#sidebar .widget-container ul {
  display: block;
  margin-left: 10px
}

blockquote {
  padding: 0 20px;
  border: 4px double #ddd;
  margin: 20px 2%;
  background: #f6f6f6
}

input, textarea {
  color: #666;
  padding: 5px;
  border: 1px solid #ccc;
  background: #f6f6f6
}

input[type="submit"] {
  cursor: pointer
}

a img, input[type="image"] {
  border: 0 none
}

img, video {
  max-width: 100%;
  height: auto
}

table {
  width: 100%
}

#content img, #content figure, #content table {
  max-width: 640px !important;
  height: auto
}

#sidebar img, #sidebar figure {
  max-width: 300px !important;
  height: auto
}

#wrapper {
  min-width: 320px;
  max-width: 1000px;
  margin: 0 auto
}

#header {
  padding: 0 2%;
  margin-bottom: 10px;
  clear: left
}

#content {
  width: 64%;
  padding: 0 2%;
  float: left
}

.page-template-sidebar-left-php #content {
  float: right
}

.page-template-sidebar-none-php #content {
  width: 96%
}

#sidebar {
  width: 30%;
  margin-right: 2%;
  float: right
}

.page-template-sidebar-left-php #sidebar {
  margin-right: 0;
  margin-left: 2%;
  float: left
}

#hsidebar, #fsidebar {
  margin: 0 2%
}

#hsidebar .widget-container, #fsidebar .widget-container {
  padding-right: 25px;
  margin-bottom: 30px;
  float: left
}

#footer {
  padding-top: 20px;
  clear: both
}

.entry-meta, .entry-meta a, .entry-footer, .comment-meta, .comment-meta a, #copyright, #copyright a {
  color: #666;
  font-size: 13px
}

#site-title a, #site-title h1 a, #site-title h1 {
  font-size: 35px;
  padding: 0;
  margin: 0
}

#site-title a:hover, #site-title h1 a:hover {
  text-decoration: none
}

#site-description {
  font-size: 18px;
  margin: 10px 0 20px
}

h4, h5, h6, #site-description, .entry-meta, .comment-meta, #form-allowed-tags code, #copyright {
  color: #aaa
}

#sidebar ul, #hsidebar ul, #fsidebar ul, #footer .menu, #footer .menu li {
  display: inline;
  list-style: none;
  padding: 0;
  margin: 0
}

.widget-title {
  padding-bottom: 5px;
  margin-top: 25px;
  margin-bottom: 8px;
  border-bottom: 1px solid #ccc
}

#respond h3 {
  clear: left
}

#footer #fmenu {
  text-align: center;
  margin-top: 20px
}

#footer .menu li a {
  color: #666;
  font-size: 16px;
  margin-right: 20px
}

#footer .menu li:last-child a {
  margin-right: 0
}

#copyright {
  text-align: center;
  padding: 20px
}

#search {
  margin-top: -4px;
  float: right
}

#sidebar #search {
  float: none
}

#s {
  width: 229px;
  height: 16px;
  border-right: 0;
  float: left
}

#s:focus {
  background: #fff
}

#searchsubmit {
  width: 60px;
  height: 28px;
  float: left
}

.error404 #content #searchform, .search #content #searchform {
  margin-top: 55px;
  float: left
}

.post {
  padding-bottom: 20px;
  border-bottom: 1px dashed #ccc;
  margin-bottom: 20px
}

.entry-footer {
  margin-top: 10px
}

.single .entry-title {
  text-align: left;
  line-height: 30px
}

.entry-title a {
  font-size: 22px
}

.nav-previous {
  text-align: left;
  float: left
}

.nav-next {
  text-align: right;
  float: right
}

.comments ul {
  padding: 0;
  margin: 0
}

.comment, .trackback, .pingback {
  list-style: none;
  padding: 10px;
  margin: 10px 0
}

.odd {
  background: #f6f6f6
}

.form-allowed-tags code {
  font-size: 12px
}

#respond input {
  width: 40%;
  display: block
}

#respond input:focus, #respond textarea:focus {
  background: #fff
}

#respond textarea {
  width: 98.125%;
  height: 100px;
  overflow: auto
}

#respond #submit {
  width: auto;
  font-weight: bold
}

.avatar {
  width: 32px;
  height: 32px;
  margin: 0 5px 5px 0;
  float: left
}

.sticky {
  padding: 15px 15px 15px 15px;
  margin-top: 20px;
  background: #f6f6f6
}

.sticky h2 {
  margin-top: 0
}

.bypostauthor {
  background: #f6f6f6
}

#wp-calendar {
  width: 100%
}

#wp-calendar td {
  text-align: center
}

#wp-calendar #prev {
  text-align: left
}

#wp-calendar #next {
  text-align: right
}

.wp-post-image {
  margin-top: 10px
}

.wp-caption {
  max-width: 640px;
  margin: 20px 0
}

.wp-caption-text, .alignleft, .aligncenter, .alignright {
  max-width: 640px;
  text-align: center
}

.gallery-caption {
  border: 1px solid #ccc
}

.alignleft {
  margin: 20px 20px 20px 0;
  float: left
}

.alignright {
  margin: 20px 0 20px 20px;
  float: right
}

.aligncenter {
  display: block;
  margin: 20px auto;
  clear: both
}

.gallery {
  margin: 20px auto !important
}

.size-thumbnail {
  margin: 5px
}

.attachment #nav-above a {
  font-size: 25px
}

.entry-links {
  clear: both
}

#menu {
  border-bottom: 1px solid #ccc;
  margin: 20px 0 0
}

#menu ul li a {
  display: inline-block;
  font-family: georgia, 'times new roman', serif;
  font-size: 18px;
  color: #09f;
  text-decoration: none;
  padding-bottom: 5px;
  margin: 0 10px 0 0
}

#menu ul li a:hover {
  color: #40b3ff;
  text-decoration: none
}

#menu ul {
  width: 100%;
  display: inline-block;
  text-align: center;
  margin: 0;
  padding: 0;
  list-style: none
}

#menu ul li {
  display: inline;
  margin: 0;
  padding: 0;
  list-style: none
}


#menu li ul {
  display: none;
  position: absolute;
  top: 22px;
  left: 0
}

#menu ul li:hover ul {
  display: inline-block
}

#menu ul ul, #menu ul li:hover ul ul, #menu ul ul li:hover ul ul {
  display: none
}

#menu ul li:hover ul, #menu ul ul li:hover ul, #menu ul ul ul li:hover ul {
  display: block;
  position: absolute;
  z-index: 2147483647
}

#menu ul li:hover ul li a, #menu ul ul li:hover ul li a, #menu ul ul ul li:hover ul li a {
  font-size: 14px;
  color: #666;
  border: #ccc 1px solid;
  margin-top: -1px;
  background: #fff
}

#menu ul ul li:hover ul li a, #menu ul ul ul li:hover ul li a {
  margin-top: -2px
}

#menu ul li:hover ul li a:hover, #menu ul ul li:hover ul li a:hover, #menu ul ul ul li:hover ul li a:hover {
  color: #888
}

#menu ul li:hover ul li a {
  width: 150px;
  padding: 5px
}

#menu ul ul li:hover ul li a {
  width: 145px;
  padding: 5px 5px 5px 10px
}

#menu ul ul ul li:hover ul li a {
  width: 140px;
  padding: 5px 5px 5px 15px
}

.current-menu-item a:hover {
  color: #0064a6 !important
}

.current-menu-item .sub-menu a {
  color: #666 !important
}

.current-menu-item .sub-menu a:hover {
  color: #888 !important
}

@media all and (max-width: 999px) {
  #content img, #content figure, #content table, #sidebar img, #sidebar figure, .wp-caption {
    width: 100% !important
  }
}

@media all and (max-width: 480px) {
  #content, #sidebar {
    width: 96%
  }
}

.screen-reader-text {
  clip: rect(1px, 1px, 1px, 1px);
  position: absolute !important
}

.comment-notes, .form-allowed-tags {
  display: none
}

.attachment #nav-above {
  display: block
}

/* Darius */
#footer {
  background-color: #D2F1CA;
}

#site-logo img {
  width: 100%;
}

#menu ul li {
  text-align: center;
  display: inline-block !important;
  background-color: #006964;
  padding: 4px 10px 1px 10px;
}

#menu ul li a {
  font-family: verdana;
  text-transform: uppercase;
  color: white;
  width: 100%;
  margin: 0;
}

#menu ul li a:hover {
  background-color: #419A95;
  color: white;
}

#menu {
  background-color: #006964;
  margin: 0;
}

#header {
  padding: 0;
  margin: 0;
}

div.home-header {
  background-color: #006964;
  font-size: 2.5em;
  color: white;
  font-family: Arial, Verdana;
}

.home-content .left {
  width: 70%;
  float: left;
}

.home-content .right {
  width: 30%;
  float: left;
}

/* CSS FROM OLD SITE */
/* Universal Cascading Style Sheet */
/* Version 1.0 - October 7, 2003 */

/* HTML Tags */
body {
  font-family: Arial, Verdana;
  font-size: 12px;
  margin: 0px;
  padding: 0px;
  background-color: #FFFFFF;
}

td, form, input, textarea, select, option {
  font-family: Arial, Verdana;
  font-size: 12px;
  color: #696969;
  VERTICAL-ALIGN: TOP;
  PADDING: 5PX;
}

hr {
  height: 1px;
}

ul, li {
  margin: 2px;
}

a {
  font-weight: normal;
}

a:link {
  color: #007671;
}

a:visited {
  color: #1CB4AE;
}

a:hover {
  color: #1CB4AE;
}

a:active {
  color: #1CB4AE;
}

h1, h2, h3, h4 {
  font-family: Arial, Verdana;
}

h1 {
  font-size: 28px;
  color: #000000;
}

h2 {
  font-size: 20px;
  color: #006964;
}

h3 {
  font-size: 16px;
  color: #006964;
}

h4 {
  font-size: 14px;
  color: #006964;
}

/* Panel Backround Colors */
.panel1 {
  background-color: #006964;
}

.panel2 {
  background-color: #E1DDD3;
}

.panel3 {
  background-color: #D2F1CA;
}

.gray {
  background-color: #F4F4F4;
}

/* Navigation */
.nav {
  text-decoration: none;
  text-align: center;
  display: block;
  color: #FFFFFF;
  background-color: #006964;
  font-weight: bold;
}

a.nav:link {
  color: #FFFFFF;
  background-color: #006964;
}

a.nav:visited {
  color: #FFFFFF;
  background-color: #006964;
}

a.nav:hover {
  color: #FFFFFF;
  background-color: #419A95;
}

a.nav:active {
  color: #FFFFFF;
  background-color: #419A95;
}

/* Module Navigation */
.modnav {
  text-decoration: none;
  display: block;
  color: #006964;
  font-weight: bold;
}

a.modnav:link {
  color: #006964;
}

a.modnav:visited {
  color: #006964;
}

a.modnav:hover {
  color: #419A95;
}

a.modnav:active {
  color: #419A95;
}

/* Custom Font Attributes */
.font1 {
  font-family: Arial, Verdana;
  font-size: 12px;
  color: #000000;
}

.font2 {
  font-family: Arial, Verdana;
  font-size: 12px;
  color: #000000;
}

.font3 {
  font-family: Arial, Verdana;
  font-size: 12px;
  color: #000000;
}

.tiny {
  font-family: Arial, Verdana;
  font-size: 10px;
}

.medium {
  font-family: Arial, Verdana;
  font-size: 14px;
}

.large {
  font-family: Arial, Verdana;
  font-size: 18px;
}

/* No Padding */
a, form, h1, h2, h3, h4 {
  margin: 0px;
  padding: 0px;
}

/* Universal Padding */

.coupon {
  border: 5px dashed #DFD5B7;
  margin: 10px;
  padding: 10px;
}

.right {
  padding-top: 10px;
}

.home-content p, .home-content h3, .home-content i {
  padding: 10px;
}

.home-header {
  text-align: center;
  padding: 10px;
  font-family: monospace;
}

.right p {
  text-align: center;
  margin: 20px 0;
}