<?php
/**
 * D&L Garden Center - Pricing Overview Page
 * Overview of all pricing options and services
 */

define('DL_GARDEN_CENTER', true);
require_once '../includes/config.php';

// Page meta data
$pageTitle = 'Pricing - ' . SITE_NAME;
$pageDescription = '2025 pricing for bulk landscaping materials at D&L Garden Center. Compare pickup and delivery prices for topsoil, mulch, sand, gravel, and stone.';
$pageKeywords = 'pricing, bulk materials, topsoil, mulch, sand, gravel, pickup, delivery, Taylor Michigan';

include '../includes/header.php';
?>

<!-- Page Header -->
<section class="page-header bg-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1>2025 Pricing</h1>
                <p class="lead">Competitive prices for all your landscaping needs</p>
            </div>
        </div>
    </div>
</section>

<!-- Pricing Options -->
<section class="pricing-options py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2>Choose Your Option</h2>
                <p class="lead">We offer both pickup and delivery options to fit your needs</p>
            </div>
        </div>
        
        <div class="row">
            <!-- Pickup Option -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100 border-0 shadow-lg">
                    <div class="card-header bg-success text-white text-center py-4">
                        <i class="bi bi-truck fs-1 mb-3"></i>
                        <h3>Pick-Up Pricing</h3>
                        <p class="mb-0">Save money by picking up yourself</p>
                    </div>
                    <div class="card-body p-4">
                        <div class="pricing-features">
                            <h5>What's Included:</h5>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success me-2"></i>Material only</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Load into your vehicle</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>5-gallon containers available</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>½ yard and full yard options</li>
                            </ul>
                            
                            <h5 class="mt-4">Sample Pricing:</h5>
                            <div class="pricing-sample">
                                <div class="d-flex justify-content-between">
                                    <span>Screened Topsoil (1 yard):</span>
                                    <strong>$32.00</strong>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Enviro Mulch (1 yard):</span>
                                    <strong>$45.00</strong>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Pool Sand (1 yard):</span>
                                    <strong>$45.00</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent text-center">
                        <a href="/pricing/pickup/" class="btn btn-success btn-lg">View Pickup Prices</a>
                    </div>
                </div>
            </div>
            
            <!-- Delivery Option -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100 border-0 shadow-lg">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <i class="bi bi-house-door fs-1 mb-3"></i>
                        <h3>Delivery Pricing</h3>
                        <p class="mb-0">Convenient delivery to your location</p>
                    </div>
                    <div class="card-body p-4">
                        <div class="pricing-features">
                            <h5>What's Included:</h5>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success me-2"></i>Material + delivery</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Dumped in your driveway</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>1-5 yard quantities</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Service area coverage</li>
                            </ul>
                            
                            <h5 class="mt-4">Sample Pricing:</h5>
                            <div class="pricing-sample">
                                <div class="d-flex justify-content-between">
                                    <span>Screened Topsoil (1 yard):</span>
                                    <strong>$85.00</strong>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Enviro Mulch (1 yard):</span>
                                    <strong>$95.00</strong>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Pool Sand (1 yard):</span>
                                    <strong>$105.00</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent text-center">
                        <a href="/pricing/delivery/" class="btn btn-primary btn-lg">View Delivery Prices</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Pricing Calculator -->
<section class="pricing-calculator py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card border-0 shadow">
                    <div class="card-header bg-warning text-dark text-center">
                        <h3><i class="bi bi-calculator me-2"></i>Quick Price Estimator</h3>
                        <p class="mb-0">Get an estimate for your project</p>
                    </div>
                    <div class="card-body p-4">
                        <form id="priceCalculator">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="material" class="form-label">Material Type</label>
                                    <select class="form-select" id="material" required>
                                        <option value="">Select material...</option>
                                        <option value="topsoil" data-pickup="32" data-delivery="85">Screened Topsoil</option>
                                        <option value="garden-mix" data-pickup="55" data-delivery="105">Premium Garden Mix</option>
                                        <option value="sand" data-pickup="45" data-delivery="105">Pool/Play Sand</option>
                                        <option value="mulch" data-pickup="45" data-delivery="95">Enviro Mulch</option>
                                        <option value="gravel" data-pickup="39" data-delivery="90">21AA Gravel</option>
                                        <option value="limestone" data-pickup="49" data-delivery="105">Clean Limestone</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="quantity" class="form-label">Quantity (yards)</label>
                                    <input type="number" class="form-control" id="quantity" min="1" max="10" value="1" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label class="form-label">Delivery Option</label>
                                    <div class="btn-group w-100" role="group">
                                        <input type="radio" class="btn-check" name="delivery" id="pickup" value="pickup" checked>
                                        <label class="btn btn-outline-success" for="pickup">Pick-Up</label>
                                        
                                        <input type="radio" class="btn-check" name="delivery" id="delivery" value="delivery">
                                        <label class="btn btn-outline-primary" for="delivery">Delivery</label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="result-section" id="priceResult" style="display: none;">
                                <div class="alert alert-info">
                                    <h5>Estimated Price:</h5>
                                    <div class="price-breakdown">
                                        <div class="d-flex justify-content-between">
                                            <span id="materialName"></span>
                                            <span id="materialPrice"></span>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span id="quantityText"></span>
                                            <span></span>
                                        </div>
                                        <hr>
                                        <div class="d-flex justify-content-between">
                                            <strong>Total Estimate:</strong>
                                            <strong id="totalPrice"></strong>
                                        </div>
                                    </div>
                                    <small class="text-muted">*Prices are estimates. Call for exact pricing and availability.</small>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Additional Information -->
<section class="pricing-info py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-4 mb-4">
                <div class="info-card text-center p-4 bg-light rounded">
                    <i class="bi bi-info-circle text-primary fs-2 mb-3"></i>
                    <h4>Important Notes</h4>
                    <ul class="list-unstyled text-start">
                        <li>• All prices plus tax</li>
                        <li>• Prices subject to change</li>
                        <li>• Call for current availability</li>
                        <li>• Bulk discounts available</li>
                    </ul>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="info-card text-center p-4 bg-light rounded">
                    <i class="bi bi-rulers text-primary fs-2 mb-3"></i>
                    <h4>Coverage Guide</h4>
                    <p><strong>1 cubic yard covers:</strong></p>
                    <ul class="list-unstyled text-start">
                        <li>• 100 sq ft at 3" deep</li>
                        <li>• 150 sq ft at 2" deep</li>
                        <li>• 300 sq ft at 1" deep</li>
                    </ul>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="info-card text-center p-4 bg-light rounded">
                    <i class="bi bi-telephone text-primary fs-2 mb-3"></i>
                    <h4>Need Help?</h4>
                    <p>Our experts can help calculate quantities and recommend materials.</p>
                    <a href="tel:<?php echo SITE_PHONE; ?>" class="btn btn-primary">
                        Call <?php echo formatPhone(SITE_PHONE); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- JavaScript for Price Calculator -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('priceCalculator');
    const materialSelect = document.getElementById('material');
    const quantityInput = document.getElementById('quantity');
    const deliveryRadios = document.querySelectorAll('input[name="delivery"]');
    const resultSection = document.getElementById('priceResult');
    
    function calculatePrice() {
        const material = materialSelect.value;
        const quantity = parseInt(quantityInput.value) || 1;
        const deliveryType = document.querySelector('input[name="delivery"]:checked').value;
        
        if (!material) {
            resultSection.style.display = 'none';
            return;
        }
        
        const option = materialSelect.selectedOptions[0];
        const pickupPrice = parseFloat(option.dataset.pickup);
        const deliveryPrice = parseFloat(option.dataset.delivery);
        
        const pricePerYard = deliveryType === 'pickup' ? pickupPrice : deliveryPrice;
        const totalPrice = pricePerYard * quantity;
        
        // Update display
        document.getElementById('materialName').textContent = option.text;
        document.getElementById('materialPrice').textContent = '$' + pricePerYard.toFixed(2) + ' per yard';
        document.getElementById('quantityText').textContent = quantity + ' yard' + (quantity > 1 ? 's' : '');
        document.getElementById('totalPrice').textContent = '$' + totalPrice.toFixed(2);
        
        resultSection.style.display = 'block';
    }
    
    // Add event listeners
    materialSelect.addEventListener('change', calculatePrice);
    quantityInput.addEventListener('input', calculatePrice);
    deliveryRadios.forEach(radio => {
        radio.addEventListener('change', calculatePrice);
    });
});
</script>

<?php include '../includes/footer.php'; ?>
