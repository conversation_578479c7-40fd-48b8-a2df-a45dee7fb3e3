#!/bin/bash

# D&L Garden Center - Database Setup Script
# Run this script to set up MariaDB for the website

echo "=== D&L Garden Center Database Setup ==="
echo ""

# Check if MariaDB is running
if ! systemctl is-active --quiet mariadb; then
    echo "Starting MariaDB service..."
    sudo systemctl start mariadb
    sudo systemctl enable mariadb
fi

echo "MariaDB is running."
echo ""

# Create database setup SQL
cat > /tmp/dlgarden_setup.sql << 'EOF'
-- Create database
CREATE DATABASE IF NOT EXISTS dlgarden_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user
CREATE USER IF NOT EXISTS 'dlgarden_user'@'localhost' IDENTIFIED BY 'dlgarden_pass_2025!';

-- Grant privileges
GRANT ALL PRIVILEGES ON dlgarden_db.* TO 'dlgarden_user'@'localhost';

-- Flush privileges
FLUSH PRIVILEGES;

-- Show databases to confirm
SHOW DATABASES;

-- Show user to confirm
SELECT User, Host FROM mysql.user WHERE User = 'dlgarden_user';
EOF

echo "Database setup SQL created."
echo ""

echo "Now running database setup..."
echo "You may be prompted for the MariaDB root password."
echo "If you haven't set one, try pressing Enter or use 'sudo mysql' instead."
echo ""

# Try to run the setup
if sudo mysql < /tmp/dlgarden_setup.sql; then
    echo ""
    echo "✅ Database setup completed successfully!"
    echo ""
    echo "Database: dlgarden_db"
    echo "User: dlgarden_user"
    echo "Password: dlgarden_pass_2025!"
    echo ""
    echo "Next step: Run the website database installer"
    echo "Visit: http://localhost:8000/sql/install.php?install=confirm"
    echo ""
else
    echo ""
    echo "❌ Database setup failed."
    echo ""
    echo "Manual setup instructions:"
    echo "1. Connect to MariaDB as root:"
    echo "   sudo mysql"
    echo ""
    echo "2. Run these commands:"
    echo "   CREATE DATABASE dlgarden_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    echo "   CREATE USER 'dlgarden_user'@'localhost' IDENTIFIED BY 'dlgarden_pass_2025!';"
    echo "   GRANT ALL PRIVILEGES ON dlgarden_db.* TO 'dlgarden_user'@'localhost';"
    echo "   FLUSH PRIVILEGES;"
    echo "   EXIT;"
    echo ""
    echo "3. Then visit: http://localhost:8000/sql/install.php?install=confirm"
fi

# Clean up
rm -f /tmp/dlgarden_setup.sql

echo ""
echo "=== Setup Complete ==="
