<?php
/**
 * D&L Garden Center - Database Installation Script
 * Run this script to set up the database and initial data
 */

// Prevent direct access in production
if (!defined('DL_GARDEN_CENTER')) {
    define('DL_GARDEN_CENTER', true);
}

require_once __DIR__ . '/../includes/config.php';

// Check if running from command line or web
$isCLI = php_sapi_name() === 'cli';

if (!$isCLI) {
    // Basic security check for web access
    if (!isset($_GET['install']) || $_GET['install'] !== 'confirm') {
        die('Access denied. Use: install.php?install=confirm');
    }
    
    echo "<h1>D&L Garden Center Database Installation</h1>\n";
    echo "<pre>\n";
}

try {
    echo "Starting database installation...\n\n";
    
    // Read SQL schema file
    $schemaFile = __DIR__ . '/schema.sql';
    if (!file_exists($schemaFile)) {
        throw new Exception("Schema file not found: $schemaFile");
    }
    
    $sql = file_get_contents($schemaFile);
    if ($sql === false) {
        throw new Exception("Could not read schema file");
    }
    
    // Connect to MySQL without specifying database first
    $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "Connected to MySQL server...\n";
    
    // Better SQL parsing - split by semicolon but handle multi-line statements
    $statements = [];
    $currentStatement = '';
    $lines = explode("\n", $sql);

    foreach ($lines as $line) {
        $line = trim($line);

        // Skip empty lines and comments
        if (empty($line) || strpos($line, '--') === 0) {
            continue;
        }

        $currentStatement .= $line . ' ';

        // If line ends with semicolon, we have a complete statement
        if (substr($line, -1) === ';') {
            $statements[] = trim($currentStatement);
            $currentStatement = '';
        }
    }

    // Add any remaining statement
    if (!empty(trim($currentStatement))) {
        $statements[] = trim($currentStatement);
    }

    $executedCount = 0;

    foreach ($statements as $statement) {
        $statement = trim($statement);

        // Skip empty statements
        if (empty($statement)) {
            continue;
        }

        try {
            $pdo->exec($statement);
            $executedCount++;

            // Show progress for major operations
            if (stripos($statement, 'CREATE DATABASE') !== false) {
                echo "✓ Database created\n";
            } elseif (stripos($statement, 'USE ') !== false) {
                echo "✓ Using database\n";
            } elseif (stripos($statement, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE[^`]*`?(\w+)`?/i', $statement, $matches);
                $tableName = $matches[1] ?? 'unknown';
                echo "✓ Table created: $tableName\n";
            } elseif (stripos($statement, 'INSERT INTO') !== false) {
                preg_match('/INSERT INTO\s+(\w+)/i', $statement, $matches);
                $tableName = $matches[1] ?? 'unknown';
                echo "✓ Sample data inserted into: $tableName\n";
            }

        } catch (PDOException $e) {
            // Continue on non-critical errors (like table already exists)
            if (stripos($e->getMessage(), 'already exists') !== false) {
                echo "⚠ Skipped (already exists): " . substr($statement, 0, 50) . "...\n";
            } else {
                echo "✗ Error executing statement: " . $e->getMessage() . "\n";
                echo "Statement: " . substr($statement, 0, 100) . "...\n";
                // Don't stop on errors, continue with next statement
            }
        }
    }
    
    echo "\nDatabase installation completed!\n";
    echo "Executed $executedCount SQL statements.\n\n";
    
    // Test the connection with the new database
    echo "Testing database connection...\n";
    $testPdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    // Check if tables were created
    $tables = $testPdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "✓ Database connection successful\n";
    echo "✓ Created " . count($tables) . " tables\n";
    
    // Show created tables
    echo "\nCreated tables:\n";
    foreach ($tables as $table) {
        echo "  - $table\n";
    }
    
    // Create default admin user
    echo "\nCreating default admin user...\n";
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    
    $stmt = $testPdo->prepare("
        INSERT INTO admin_users (username, email, password_hash, first_name, last_name, role) 
        VALUES (?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE password_hash = VALUES(password_hash)
    ");
    
    $stmt->execute(['admin', '<EMAIL>', $adminPassword, 'Admin', 'User', 'admin']);
    echo "✓ Default admin user created (username: admin, password: admin123)\n";
    echo "⚠ IMPORTANT: Change the default admin password immediately!\n\n";
    
    // Show next steps
    echo "=== NEXT STEPS ===\n";
    echo "1. Change the default admin password\n";
    echo "2. Update the configuration in includes/config.php if needed\n";
    echo "3. Set up proper file permissions\n";
    echo "4. Configure your web server\n";
    echo "5. Test the website functionality\n\n";
    
    echo "Installation completed successfully!\n";
    
} catch (Exception $e) {
    echo "✗ Installation failed: " . $e->getMessage() . "\n";
    echo "Please check your database configuration and try again.\n";
    
    if (!$isCLI) {
        echo "</pre>\n";
    }
    exit(1);
}

if (!$isCLI) {
    echo "</pre>\n";
    echo "<p><strong>Installation completed!</strong></p>";
    echo "<p><a href='/'>Visit your website</a> | <a href='/admin/'>Admin Panel</a></p>";
}
?>
