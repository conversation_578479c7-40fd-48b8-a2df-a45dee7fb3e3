<?php
/**
 * D&L Garden Center - Simple Database Installation
 * Creates tables one by one for better error handling
 */

// Prevent direct access in production
if (!defined('DL_GARDEN_CENTER')) {
    define('DL_GARDEN_CENTER', true);
}

require_once __DIR__ . '/../includes/config.php';

// Check if running from command line or web
$isCLI = php_sapi_name() === 'cli';

if (!$isCLI) {
    // Basic security check for web access
    if (!isset($_GET['install']) || $_GET['install'] !== 'confirm') {
        die('Access denied. Use: simple-install.php?install=confirm');
    }
    
    echo "<h1>D&L Garden Center Simple Database Installation</h1>\n";
    echo "<pre>\n";
}

try {
    echo "Starting simple database installation...\n\n";
    
    // Connect to database
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✓ Connected to database: " . DB_NAME . "\n\n";
    
    // Define tables to create
    $tables = [
        'categories' => "
            CREATE TABLE categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                slug VARCHAR(100) NOT NULL UNIQUE,
                description TEXT,
                parent_id INT NULL,
                sort_order INT DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_parent_id (parent_id),
                INDEX idx_slug (slug),
                INDEX idx_active (is_active),
                FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
            ) ENGINE=InnoDB
        ",
        
        'products' => "
            CREATE TABLE products (
                id INT AUTO_INCREMENT PRIMARY KEY,
                category_id INT NOT NULL,
                name VARCHAR(200) NOT NULL,
                slug VARCHAR(200) NOT NULL UNIQUE,
                description TEXT,
                short_description VARCHAR(500),
                sku VARCHAR(50),
                product_type ENUM('bulk_material', 'bagged_supply', 'equipment', 'plant', 'service') NOT NULL,
                unit_type VARCHAR(50),
                weight DECIMAL(8,2),
                dimensions VARCHAR(100),
                is_featured BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                meta_title VARCHAR(200),
                meta_description VARCHAR(300),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_category (category_id),
                INDEX idx_slug (slug),
                INDEX idx_type (product_type),
                INDEX idx_featured (is_featured),
                INDEX idx_active (is_active),
                FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT
            ) ENGINE=InnoDB
        ",
        
        'product_pricing' => "
            CREATE TABLE product_pricing (
                id INT AUTO_INCREMENT PRIMARY KEY,
                product_id INT NOT NULL,
                quantity_min DECIMAL(8,2) NOT NULL DEFAULT 1,
                quantity_max DECIMAL(8,2),
                unit VARCHAR(50) NOT NULL,
                pickup_price DECIMAL(8,2),
                delivery_price DECIMAL(8,2),
                is_active BOOLEAN DEFAULT TRUE,
                effective_date DATE,
                expires_date DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_product (product_id),
                INDEX idx_active (is_active),
                INDEX idx_effective (effective_date),
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
            ) ENGINE=InnoDB
        ",
        
        'gallery_categories' => "
            CREATE TABLE gallery_categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                slug VARCHAR(100) NOT NULL UNIQUE,
                description TEXT,
                sort_order INT DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_slug (slug),
                INDEX idx_active (is_active)
            ) ENGINE=InnoDB
        ",
        
        'gallery_images' => "
            CREATE TABLE gallery_images (
                id INT AUTO_INCREMENT PRIMARY KEY,
                category_id INT,
                title VARCHAR(200),
                filename VARCHAR(255) NOT NULL,
                original_filename VARCHAR(255),
                alt_text VARCHAR(200),
                caption TEXT,
                sort_order INT DEFAULT 0,
                is_featured BOOLEAN DEFAULT FALSE,
                file_size INT,
                width INT,
                height INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_category (category_id),
                INDEX idx_featured (is_featured),
                INDEX idx_sort (sort_order),
                FOREIGN KEY (category_id) REFERENCES gallery_categories(id) ON DELETE SET NULL
            ) ENGINE=InnoDB
        ",
        
        'content_blocks' => "
            CREATE TABLE content_blocks (
                id INT AUTO_INCREMENT PRIMARY KEY,
                page VARCHAR(100) NOT NULL,
                section VARCHAR(100) NOT NULL,
                title VARCHAR(200),
                content TEXT,
                content_type ENUM('text', 'html', 'markdown') DEFAULT 'html',
                sort_order INT DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_page_section (page, section),
                INDEX idx_active (is_active),
                INDEX idx_sort (sort_order)
            ) ENGINE=InnoDB
        ",
        
        'admin_users' => "
            CREATE TABLE admin_users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                email VARCHAR(100) NOT NULL UNIQUE,
                password_hash VARCHAR(255) NOT NULL,
                first_name VARCHAR(50),
                last_name VARCHAR(50),
                role ENUM('admin', 'editor', 'viewer') DEFAULT 'editor',
                is_active BOOLEAN DEFAULT TRUE,
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_username (username),
                INDEX idx_email (email),
                INDEX idx_active (is_active)
            ) ENGINE=InnoDB
        ",
        
        'contact_submissions' => "
            CREATE TABLE contact_submissions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                subject VARCHAR(200),
                message TEXT NOT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                is_read BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_read (is_read),
                INDEX idx_created (created_at)
            ) ENGINE=InnoDB
        ",
        
        'settings' => "
            CREATE TABLE settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) NOT NULL UNIQUE,
                setting_value TEXT,
                setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
                description TEXT,
                is_public BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_key (setting_key),
                INDEX idx_public (is_public)
            ) ENGINE=InnoDB
        "
    ];
    
    // Create tables
    foreach ($tables as $tableName => $sql) {
        try {
            $pdo->exec("DROP TABLE IF EXISTS $tableName");
            $pdo->exec($sql);
            echo "✓ Table created: $tableName\n";
        } catch (PDOException $e) {
            echo "✗ Error creating table $tableName: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n";
    
    // Insert sample data
    echo "Inserting sample data...\n";
    
    // Categories
    $pdo->exec("
        INSERT INTO categories (name, slug, description, sort_order) VALUES
        ('Bulk Materials', 'bulk-materials', 'Topsoil, sand, gravel, and other bulk landscaping materials', 1),
        ('Mulch', 'mulch', 'Various types of mulch for landscaping', 2),
        ('Bagged Supplies', 'bagged-supplies', 'Pre-packaged landscaping and garden supplies', 3),
        ('Equipment', 'equipment', 'Lawn and garden equipment', 4)
    ");
    echo "✓ Sample categories inserted\n";
    
    // Gallery categories
    $pdo->exec("
        INSERT INTO gallery_categories (name, slug, description, sort_order) VALUES
        ('Products', 'products', 'Product photos and demonstrations', 1),
        ('Facilities', 'facilities', 'Our garden center facilities', 2),
        ('Projects', 'projects', 'Customer landscaping projects', 3)
    ");
    echo "✓ Gallery categories inserted\n";
    
    // Settings
    $pdo->exec("
        INSERT INTO settings (setting_key, setting_value, setting_type, description, is_public) VALUES
        ('site_name', 'D&L Garden Center', 'string', 'Website name', TRUE),
        ('delivery_radius', '25', 'number', 'Delivery radius in miles', TRUE)
    ");
    echo "✓ Settings inserted\n";
    
    // Create default admin user
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("
        INSERT INTO admin_users (username, email, password_hash, first_name, last_name, role) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    $stmt->execute(['admin', '<EMAIL>', $adminPassword, 'Admin', 'User', 'admin']);
    echo "✓ Default admin user created (username: admin, password: admin123)\n";
    
    echo "\n✅ Installation completed successfully!\n";
    echo "Database: " . DB_NAME . "\n";
    echo "Tables created: " . count($tables) . "\n";
    echo "Admin user: admin / admin123\n";
    
} catch (Exception $e) {
    echo "✗ Installation failed: " . $e->getMessage() . "\n";
    exit(1);
}

if (!$isCLI) {
    echo "</pre>\n";
    echo "<p><strong>Installation completed!</strong></p>";
    echo "<p><a href='/'>Visit your website</a></p>";
}
?>
