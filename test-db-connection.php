<?php
/**
 * D&L Garden Center - Database Connection Test
 * Test if we can connect to the database
 */

echo "<h1>D&L Garden Center - Database Connection Test</h1>\n";
echo "<pre>\n";

// Test 1: Check if PDO MySQL extension is available
echo "1. Checking PDO MySQL extension...\n";
if (extension_loaded('pdo_mysql')) {
    echo "   ✅ PDO MySQL extension is loaded\n";
} else {
    echo "   ❌ PDO MySQL extension is NOT loaded\n";
    echo "   Install with: sudo apt install php-mysql\n";
}

echo "\n";

// Test 2: Try to connect to MariaDB with default settings
echo "2. Testing MariaDB connection...\n";

$configs = [
    [
        'host' => 'localhost',
        'user' => 'dlgarden_user',
        'pass' => 'dlgarden_pass_2025!',
        'db' => 'dlgarden_db',
        'name' => 'D&L Garden Center Database'
    ],
    [
        'host' => 'localhost',
        'user' => 'root',
        'pass' => '',
        'db' => 'mysql',
        'name' => 'Root (no password)'
    ],
    [
        'host' => 'localhost',
        'user' => 'root',
        'pass' => 'root',
        'db' => 'mysql',
        'name' => 'Root (password: root)'
    ]
];

$connected = false;

foreach ($configs as $config) {
    echo "   Testing {$config['name']}...\n";
    
    try {
        $dsn = "mysql:host={$config['host']};charset=utf8mb4";
        if (!empty($config['db'])) {
            $dsn .= ";dbname={$config['db']}";
        }
        
        $pdo = new PDO($dsn, $config['user'], $config['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        $version = $pdo->query("SELECT VERSION() as version")->fetch();
        echo "   ✅ Connected successfully!\n";
        echo "   📊 MariaDB Version: {$version['version']}\n";
        
        // Check if our database exists
        if ($config['user'] === 'dlgarden_user') {
            $tables = $pdo->query("SHOW TABLES")->fetchAll();
            echo "   📋 Tables in dlgarden_db: " . count($tables) . "\n";
            if (count($tables) > 0) {
                foreach ($tables as $table) {
                    echo "      - " . array_values($table)[0] . "\n";
                }
            }
        }
        
        $connected = true;
        break;
        
    } catch (PDOException $e) {
        echo "   ❌ Connection failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

if (!$connected) {
    echo "❌ Could not connect to MariaDB with any configuration.\n\n";
    echo "Setup Instructions:\n";
    echo "1. Run the setup script: ./setup-database.sh\n";
    echo "2. Or manually set up the database:\n";
    echo "   sudo mysql\n";
    echo "   CREATE DATABASE dlgarden_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n";
    echo "   CREATE USER 'dlgarden_user'@'localhost' IDENTIFIED BY 'dlgarden_pass_2025!';\n";
    echo "   GRANT ALL PRIVILEGES ON dlgarden_db.* TO 'dlgarden_user'@'localhost';\n";
    echo "   FLUSH PRIVILEGES;\n";
    echo "   EXIT;\n";
} else {
    echo "✅ Database connection successful!\n\n";
    echo "Next Steps:\n";
    echo "1. Install database schema: http://localhost:8000/sql/install.php?install=confirm\n";
    echo "2. Test the website: http://localhost:8000\n";
}

echo "\n";
echo "Current PHP Configuration:\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "PDO Drivers: " . implode(', ', PDO::getAvailableDrivers()) . "\n";

echo "</pre>\n";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
pre { background: #f5f5f5; padding: 15px; border-radius: 5px; }
h1 { color: #198754; }
</style>
